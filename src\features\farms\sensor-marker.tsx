import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  IconBattery1,
  IconBattery2,
  IconBattery4,
  IconC<PERSON>,
} from "@tabler/icons-react";
import { AdvancedMarker } from "@vis.gl/react-google-maps";
import { FarmStatus, MLocation, SensorItemData } from "./types";
import { SatelliteDish, SignalHigh } from "lucide-react";
import { SensorIcon } from "@/components/map/sensor-icon";

type Props = {
  data: SensorItemData;
  farmStatus?: FarmStatus;
};

function SensorMarker({ data, farmStatus }: Props) {
  const batteryStatusIcons = {
    ["good"]: <IconBattery4 size={24} className="text-primary" />,
    ["warning"]: <IconBattery2 size={24} className="text-warning" />,
    ["danger"]: <IconBattery1 size={24} className="text-danger" />,
  };

  const getBatteryStatus = (
    battery?: number
  ): "good" | "warning" | "danger" => {
    if (battery === undefined) return "good"; // Default to good if no battery data
    if (battery > 50) return "good";
    if (battery > 20) return "warning";
    return "danger";
  };

  return (
    <AdvancedMarker position={data.location as MLocation}>
      <Popover>
        <PopoverTrigger asChild>
          <div
            className="relative h-8 w-fit max-w-8 p-1 rounded-full flex items-center justify-center bg-background backdrop-blur-sm origin-bottom"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="p-1 rounded-full cursor-pointer hover:scale-110 transition-transform bg-green-500">
              <SensorIcon className="size-4 text-black" size={16} />
            </div>
            <div className="absolute bottom-0 left-1/2 w-0 h-0 border-8 border-background rounded-none rounded-tr-[5px] -z-[1] transition-all translate-y-[22%] translate-x-[-50%] rotate-[45deg] backdrop-blur-sm " />
          </div>
        </PopoverTrigger>
        <PopoverContent className="relative bg-background/50 backdrop-blur-xl p-2 rounded-xl border border-foreground/10">
          <div className="flex items-center justify-between px-1 pb-1">
            <p className="text-sm font-semibold text-accent-foreground">
              {data.name}
            </p>
          </div>
          <div className="grid grid-cols-2 gap-2">
            {/* Signal */}
            <div className="bg-background p-2 rounded-lg shadow-muted">
              <p className="font-semibold text-sm">Signal</p>
              <div className="flex items-center gap-1">
                <SignalHigh strokeWidth={2} className="size-5 text-primary" />
                <span className="text-sm font-medium">High</span>
              </div>
            </div>

            {/* Battery */}
            {data?.battery && (
              <div className="bg-background p-2 rounded-lg shadow-muted">
                <p className="font-semibold text-sm">Battery</p>
                <div className="flex items-center gap-1">
                  {batteryStatusIcons[getBatteryStatus(data.battery)]}
                  <span className="text-sm font-medium">{data.battery}%</span>
                </div>
              </div>
            )}

            {/* Value */}
            <div className="bg-background p-2 rounded-lg shadow-muted col-span-2">
              <p className="font-semibold text-sm">{data.valueName}</p>
              <div className="flex items-center gap-1">
                <SatelliteDish className="text-primary size-4" />
                <span className="text-sm font-medium">{data.value}</span>
                <span className="text-xs font-medium">{data.unit}</span>
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </AdvancedMarker>
  );
}

export default SensorMarker;
