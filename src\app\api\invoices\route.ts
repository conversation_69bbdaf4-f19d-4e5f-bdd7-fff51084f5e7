import { NextRequest, NextResponse } from 'next/server';

// ERPNext API configuration
const ERPNEXT_BASE_URL = process.env.NEXT_ERPNEXT_BASE_URL;
const API_TOKEN = process.env.NEXT_ERPNEXT_API_TOKEN;

// Define the ERPNext Purchase Invoice response type
interface ERPNextInvoice {
  name: string;
  supplier: string;
  bill_no: string;
  remarks: string;
  posting_date: string;
  due_date: string;
  bill_date: string;
  base_net_total: number;
  grand_total: number;
  status: string;
}

interface ERPNextResponse {
  data: ERPNextInvoice[];
}

// Helper function to get total count of invoices
async function getTotalInvoiceCount(filters: any[] = []): Promise<number> {
  try {
    const countUrl = `${ERPNEXT_BASE_URL}/api/method/frappe.client.get_count`;

    const requestBody: any = {
      doctype: "Purchase Invoice"
    };

    // Add filters if they exist
    if (filters.length > 0) {
      requestBody.filters = filters;
    }

    const response = await fetch(countUrl, {
      method: 'POST',
      headers: {
        'Authorization': `token ${API_TOKEN}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`ERPNext count API error: ${response.status} ${response.statusText}`);
    }

    const countData = await response.json();
    return countData.message || 0;
  } catch (error) {
    console.error('Error getting invoice count:', error);
    return 0;
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // Extract query parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search') || '';
    const suppliers = searchParams.get('suppliers') || '';
    const statuses = searchParams.get('statuses') || '';
    const date_from = searchParams.get('date_from') || '';
    const date_to = searchParams.get('date_to') || '';
    const order_by = searchParams.get('order_by') || 'creation desc';

    // Build ERPNext API URL with fields
    const fields = [
      'name',
      'supplier',
      'bill_no', 
      'remarks',
      'posting_date',
      'due_date',
      'bill_date',
      'base_net_total',
      'grand_total',
      'status'
    ];
    
    let apiUrl = `${ERPNEXT_BASE_URL}/api/resource/Purchase%20Invoice?fields=${encodeURIComponent(JSON.stringify(fields))}`;
    
    // Add pagination
    apiUrl += `&limit_page_length=${limit}`;
    if (page > 1) {
      apiUrl += `&limit_start=${(page - 1) * limit}`;
    }

    // Add filters
    const filters = [];
    
    if (search) {
      // ERPNext search across multiple fields
      filters.push(['supplier', 'like', `%${search}%`]);
    }
    
    if (suppliers) {
      const supplierArray = suppliers.split('.');
      if (supplierArray.length > 0) {
        filters.push(['supplier', 'in', supplierArray]);
      }
    }
    
    if (statuses) {
      const statusArray = statuses.split('.');
      if (statusArray.length > 0) {
        filters.push(['status', 'in', statusArray]);
      }
    }
    
    if (date_from) {
      filters.push(['posting_date', '>=', date_from]);
    }
    
    if (date_to) {
      filters.push(['posting_date', '<=', date_to]);
    }

    if (filters.length > 0) {
      apiUrl += `&filters=${encodeURIComponent(JSON.stringify(filters))}`;
    }

    if (order_by) {
      apiUrl += `&order_by=${encodeURIComponent(order_by)}`;
    }

    // Get total count and invoice data in parallel
    const [countPromise, dataPromise] = await Promise.all([
      getTotalInvoiceCount(filters),
      fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Authorization': `token ${API_TOKEN}`,
          'Content-Type': 'application/json',
        },
      })
    ]);

    if (!dataPromise.ok) {
      throw new Error(`ERPNext API error: ${dataPromise.status} ${dataPromise.statusText}`);
    }

    const data: ERPNextResponse = await dataPromise.json();
    const totalCount = countPromise;
    
    // Transform the data to match our expected format
    const transformedInvoices = data.data.map((invoice: ERPNextInvoice) => ({
      id: invoice.name,
      doctype: 'Purchase Invoice',
      naming_series: 'PI-',
      supplier: invoice.supplier,
      supplier_address: '',
      posting_date: invoice.posting_date,
      due_date: invoice.due_date,
      bill_no: invoice.bill_no,
      bill_date: invoice.bill_date,
      currency: 'THB',
      conversion_rate: 1,
      company: '',
      cost_center: '',
      project: '',
      taxes_and_charges: '',
      items: [],
      taxes: [],
      total_qty: 0,
      base_net_total: invoice.base_net_total,
      net_total: invoice.base_net_total,
      base_total_taxes_and_charges: 0,
      total_taxes_and_charges: 0,
      base_grand_total: invoice.grand_total,
      grand_total: invoice.grand_total,
      outstanding_amount: 0,
      status: invoice.status,
      remarks: invoice.remarks || '',
    }));

    // Return the response in the expected format
    return NextResponse.json({
      success: true,
      time: new Date().toISOString(),
      message: 'Invoice data from ERPNext',
      total_invoices: totalCount,
      offset: (page - 1) * limit,
      limit,
      invoices: transformedInvoices,
    });

  } catch (error) {
    console.error('Error fetching invoices from ERPNext:', error);
    
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to fetch invoices from ERPNext',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
