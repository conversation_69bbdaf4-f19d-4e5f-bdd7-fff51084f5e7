"use client";

import { FarmData, MLocation } from "@/features/farms/types";
import {
  APIProvider,
  ColorScheme,
  Map,
  Marker, // Import Marker instead of Overlay
  InfoWindow, // Add this import
  useMap,
} from "@vis.gl/react-google-maps";
import { useTheme } from "next-themes";
import Image from "next/image"; // Add this import at the top
import { Fragment, useEffect, useMemo, useState } from "react";
import useFarmStore from "./store";
import Polygon from "@/components/map/polygon";
import SensorMarker from "./sensor-marker";
import FarmAnalyzedModal from "./farm-analyzed-modal";
import { useSearchParams } from "next/navigation"; // Add this import

export function calculateFarmCenter(locations: MLocation[]): MLocation | null {
  if (locations.length === 0) return null;
  let area = 0,
    lat = 0,
    lng = 0;
  const n = locations.length;
  for (let i = 0; i < n; i++) {
    const { lat: x0, lng: y0 } = locations[i];
    const { lat: x1, lng: y1 } = locations[(i + 1) % n];
    const a = x0 * y1 - x1 * y0;
    area += a;
    lat += (x0 + x1) * a;
    lng += (y0 + y1) * a;
  }
  area = area / 2;
  if (area === 0) {
    // fallback to average if degenerate polygon
    return {
      lat: locations.reduce((sum, l) => sum + l.lat, 0) / n,
      lng: locations.reduce((sum, l) => sum + l.lng, 0) / n,
    };
  }
  return {
    lat: lat / (6 * area),
    lng: lng / (6 * area),
  };
}

function FarmMap() {
  const { farms, selectedFarm } = useFarmStore();
  const theme = useTheme().resolvedTheme;

  const _center = useMemo(() => {
    if (!selectedFarm?.location) return undefined;
    return calculateFarmCenter(selectedFarm.location);
  }, [selectedFarm?.location]);

  const [mapCenter, setMapCenter] = useState(_center);
  const [userInteracted, setUserInteracted] = useState(false);

  // State to track which farm's polygon is selected
  const [activeFarmId, setActiveFarmId] = useState<string | null>(null);

  // Find the active farm data for modal
  const activeFarm = farms.find((f) => f.id === activeFarmId);

  useEffect(() => {
    if (!selectedFarm?.location) return;
    const center = calculateFarmCenter(selectedFarm.location);
    setMapCenter(center);
  }, [selectedFarm?.location]);

  // Move streetHeatmapPoints outside, that's fine
  const streetHeatmapPoints = [
    { lat: 12.649653, lng: 102.14926 },
    { lat: 12.649705, lng: 102.149408 },
    { lat: 12.649756, lng: 102.149556 },
    { lat: 12.649881, lng: 102.149341 },
    { lat: 12.649824, lng: 102.149206 },
    { lat: 12.649807, lng: 102.149386 },
    { lat: 12.651858, lng: 102.149723 },
    { lat: 12.651744, lng: 102.149813 },
    { lat: 12.651884, lng: 102.149885 },
    { lat: 12.65078, lng: 102.148762 },
    { lat: 12.65099, lng: 102.148676 },
    { lat: 12.65112, lng: 102.148783 },
    { lat: 12.651199, lng: 102.148885 },
    { lat: 12.650895, lng: 102.148568 },
    { lat: 12.650745, lng: 102.148655 },
    { lat: 12.650603, lng: 102.148567 },
    { lat: 12.650456, lng: 102.148475 },
    { lat: 12.650312, lng: 102.148384 },
    { lat: 12.650168, lng: 102.148292 },
  ];

  // Child component to access useMap() inside <APIProvider>
  function MapContent() {
    const map = useMap();
    const searchParams = useSearchParams();
    const infoId = searchParams.get("info_id");

    // Only show InfoWindow if info_id matches
    const showInfo = infoId === "b2b7266e-53f1-4c25-933d-6f342c2f3b57";
    const [zoomImage, setZoomImage] = useState(false);

    useEffect(() => {
      if (!map || !window.google || !window.google.maps.visualization) return;

      const heatmap = new window.google.maps.visualization.HeatmapLayer({
        data: streetHeatmapPoints.map(
          (p) => new window.google.maps.LatLng(p.lat, p.lng)
        ),
        map,
        radius: 40,
        opacity: 0.7,
      });

      return () => {
        heatmap.setMap(null);
      };
    }, [map]);

    // Example: Place the InfoWindow at a specific location (e.g., Plot 2, Garden A)
    const infoWindowPosition = { lat: 12.65099, lng: 102.148676 }; // Adjust to your actual Plot 2, Garden A location

    return (
      <>
        {farms.map((farm: FarmData) => {
          const center = calculateFarmCenter(farm.location);
          return (
            <Fragment key={farm.id}>
              <Polygon
                id={farm.id}
                status={farm.status}
                location={farm.location}
                onClick={setActiveFarmId}
              />
              {center && (
                <Marker
                  position={{ lat: center.lat, lng: center.lng }}
                  label={{
                    text: farm.name,
                    color: "#fff",
                    fontWeight: "bold",
                    fontSize: "16px",
                    className: "farm-label",
                  }}
                  icon={{
                    url: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAAEklEQVR42mP8/5+hHgAHggJ/PtXl2wAAAABJRU5ErkJggg==",
                    scaledSize: new window.google.maps.Size(1, 1),
                  }}
                />
              )}
              {Object.values(farm.sensors).map((sensor) => (
                <SensorMarker
                  key={sensor.key}
                  data={sensor}
                  farmStatus={farm.status}
                />
              ))}
            </Fragment>
          );
        })}

        {/* InfoWindow for AI camera alert */}
        {showInfo && (
          <InfoWindow
            position={infoWindowPosition}
            onCloseClick={() => {
              // Optionally, you can update the URL to remove info_id here
              // For now, just hide the InfoWindow by navigating away or reloading without the param
            }}
          >
            <div className="max-w-xs flex items-start gap-3">
              <div>
                <Image
                  src="/assets/pest_infestation.webp"
                  alt="Pest infestation"
                  width={56}
                  height={56}
                  className="rounded cursor-zoom-in border border-orange-200"
                  onClick={() => setZoomImage(true)}
                  style={{ objectFit: "cover", width: "56px", height: "56px" }}
                />
              </div>
              <div>
                <div className="font-semibold mb-1 text-orange-700">
                  AI camera detected abnormal bite marks on leaves, suspected
                  pest infestation.
                </div>
                <div className="text-xs text-gray-500">
                  Location: Plot 2, Suan Thong Durian Garden
                </div>
              </div>
            </div>
          </InfoWindow>
        )}
        {/* Zoom modal OUTSIDE InfoWindow */}
        {zoomImage && (
          <div
            className="fixed inset-0 z-50 flex items-center justify-center bg-black/70"
            onClick={() => setZoomImage(false)}
          >
            <Image
              src="/assets/pest_infestation.webp"
              alt="Pest infestation zoom"
              width={480}
              height={480}
              className="rounded shadow-lg max-h-[90vh] max-w-[90vw] cursor-zoom-out"
              style={{ objectFit: "contain" }}
            />
          </div>
        )}
      </>
    );
  }

  return (
    <Fragment>
      <APIProvider
        apiKey={process.env.NEXT_PUBLIC_GOOGLE_MAP_API_KEY as string}
        libraries={["visualization"]}
      >
        <Map
          defaultZoom={18}
          disableDefaultUI
          mapTypeId={"satellite"}
          keyboardShortcuts={false}
          disableDoubleClickZoom={true}
          style={{ width: "100%", height: "100%" }}
          onDragend={() => setUserInteracted(false)}
          onDragstart={() => setUserInteracted(true)}
          center={userInteracted ? undefined : mapCenter}
          mapId={process.env.NEXT_PUBLIC_GOOGLE_MAP_ID as string}
          colorScheme={theme === "dark" ? ColorScheme.DARK : ColorScheme.LIGHT}
          onCenterChanged={(location) => setMapCenter(location.detail.center)}
        >
          <MapContent />
        </Map>
        {activeFarm && (
          <FarmAnalyzedModal
            isOpen={!!activeFarmId}
            farm={activeFarm}
            onOpenChange={(open) => {
              if (!open) setActiveFarmId(null);
            }}
          />
        )}
      </APIProvider>
    </Fragment>
  );
}

export default FarmMap;
