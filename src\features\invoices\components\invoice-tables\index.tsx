'use client';

import { DataTable } from '@/components/ui/table/data-table';
import { DataTableToolbar } from '@/components/ui/table/data-table-toolbar';
import { AutoRefreshControls } from '@/components/auto-refresh-controls';

import { useDataTable } from '@/hooks/use-data-table';

import { ColumnDef } from '@tanstack/react-table';
import { parseAsInteger, useQueryState } from 'nuqs';

interface InvoiceTableParams<TData, TValue> {
  data: TData[];
  totalItems: number;
  columns: ColumnDef<TData, TValue>[];
  newInvoiceIds?: Set<string>;
  onRefresh?: () => void | Promise<void>;
}

export function InvoiceTable<TData, TValue>({
  data,
  totalItems,
  columns,
  newInvoiceIds,
  onRefresh,
}: InvoiceTableParams<TData, TValue>) {
  const [pageSize] = useQueryState('perPage', parseAsInteger.withDefault(10));

  const pageCount = Math.ceil(totalItems / pageSize);

  const { table } = useDataTable({
    data,
    columns,
    pageCount: pageCount,
    shallow: false, //Setting to false triggers a network request with the updated querystring.
    debounceMs: 500,
  });

  return (
    <DataTable table={table} newInvoiceIds={newInvoiceIds} totalItems={totalItems}>
      <DataTableToolbar table={table}>
        {onRefresh && (
          <AutoRefreshControls onRefresh={onRefresh} className='ml-auto' />
        )}
      </DataTableToolbar>
    </DataTable>
  );
}
