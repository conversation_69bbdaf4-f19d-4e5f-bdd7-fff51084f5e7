import { buttonVariants } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  Leaf,
  TreePine,
  Shield,
  Eye,
  Activity,
  BarChart3,
  Sparkles,
  LeafyGreen,
  Zap,
} from 'lucide-react';
import type { Metadata } from 'next';
import Link from 'next/link';
import UserAuthForm from './user-auth-form';

export const metadata: Metadata = {
  title: 'AgriWise ERP - Sign In',
  description:
    'Sign in to access comprehensive durian farm management and agricultural ERP solutions.',
};

export default function SignInViewPage() {
  return (
    <div className='relative min-h-screen flex-col items-center justify-center md:grid lg:max-w-none lg:grid-cols-2 lg:px-0'>
      <Link
        href='/dashboard'
        className={cn(
          buttonVariants({ variant: 'ghost' }),
          'absolute top-4 right-4 hidden md:top-8 md:right-8 text-white/90 hover:text-white hover:bg-white/15 backdrop-blur-md border border-white/20 z-50 transition-all duration-300 shadow-lg'
        )}
      >
        Dashboard
      </Link>

      <div className='relative hidden min-h-screen flex-col p-0 text-gray-800 lg:flex overflow-y-auto'>
        {/* --- YouTube video background --- */}
        <div className='absolute inset-0 -z-10 pointer-events-none overflow-hidden'>
          <iframe
            src='https://www.youtube.com/embed/De_-bXyBBOc?autoplay=1&mute=1&controls=0&loop=1&playlist=De_-bXyBBOc&modestbranding=1&showinfo=0&vq=hd2160&start=245'
            title='AgriWise ERP Background'
            allow='autoplay; encrypted-media'
            allowFullScreen
            className='absolute inset-0 w-full h-full'
            style={{
              width: '100vw',
              height: '100vh',
              minWidth: '100%',
              minHeight: '100%',
              objectFit: 'cover',
              pointerEvents: 'none',
              border: 'none',
              zIndex: -1,
              top: 0,
              left: '-50%',
            }}
          />
        </div>
        {/* --- Overlay gradient: white to green --- */}
        <div className='absolute inset-0 pointer-events-none z-0'>
          <div
            className='w-full h-full absolute inset-0'
            style={{
              background:
                'linear-gradient(to bottom, rgba(255,255,255,0.92) 0%, rgba(132,204,22,0.55) 60%, rgba(22,163,74,0.65) 100%)',
              zIndex: 1,
            }}
          />
        </div>

        {/* --- Main content --- */}
        <div className='relative z-20 flex flex-col justify-center h-full px-16 py-12 space-y-10 left-content'>
          {/* Logo and Title */}
          <div className='flex items-center space-x-5 mb-2'>
            <div className='relative'>
              <div className='w-16 h-16 rounded-2xl bg-gradient-to-br from-lime-500 to-green-600 flex items-center justify-center shadow-xl shadow-lime-200/40'>
                <LeafyGreen className='text-white size-12' />
              </div>
              <div className='absolute -inset-1 bg-lime-400/20 rounded-2xl blur-sm opacity-80' />
            </div>
            <div>
              <h1 className='text-4xl font-extrabold tracking-tight text-primary'>
                AgriWise ERP
              </h1>
              <p className='text-gray-700 text-lg font-medium mt-1'>
                Modern System for Smart Agriculture
              </p>
            </div>
          </div>

          {/* Introduction */}
          <div className='bg-white/90 backdrop-blur-md mt-8 rounded-2xl p-7 border border-white/70 shadow-lg shadow-lime-100/40'>
            <h2 className='text-2xl font-bold text-lime-700 mb-2'>
              Transform Your Agricultural Business
            </h2>
            <p className='text-gray-800 text-base leading-relaxed'>
              AgriWise ERP is an integrated platform designed for modern
              agriculture enterprises. Manage your entire farm operation—from
              cultivation, inventory, and workforce to finance and export—on a
              single, intelligent system.
              <br />
              <span className='text-lime-700 font-semibold'>
                Boost productivity, ensure traceability, and make data-driven
                decisions for a sustainable future.
              </span>
            </p>
          </div>

          {/* Key Features */}
          <div className='grid grid-cols-1 gap-5 md:grid-cols-2'>
            <div className='flex items-start space-x-4 bg-white/80 rounded-xl p-5 border border-lime-100 shadow hover:shadow-lg transition'>
              <Eye className='h-7 w-7 text-lime-600 flex-shrink-0' />
              <div>
                <div className='font-semibold text-gray-900 text-lg'>
                  Smart Farm Monitoring
                </div>
                <div className='text-gray-700 text-sm'>
                  AI-powered pest detection, real-time crop health, and
                  environmental analytics.
                </div>
              </div>
            </div>
            <div className='flex items-start space-x-4 bg-white/80 rounded-xl p-5 border border-lime-100 shadow hover:shadow-lg transition'>
              <BarChart3 className='h-7 w-7 text-green-600 flex-shrink-0' />
              <div>
                <div className='font-semibold text-gray-900 text-lg'>
                  Financial & Supply Chain
                </div>
                <div className='text-gray-700 text-sm'>
                  Integrated procurement, inventory, and export management with
                  real-time financials.
                </div>
              </div>
            </div>
            <div className='flex items-start space-x-4 bg-white/80 rounded-xl p-5 border border-lime-100 shadow hover:shadow-lg transition'>
              <Activity className='h-7 w-7 text-emerald-600 flex-shrink-0' />
              <div>
                <div className='font-semibold text-gray-900 text-lg'>
                  Operations Dashboard
                </div>
                <div className='text-gray-700 text-sm'>
                  Unified view of workforce, machinery, and business performance
                  metrics.
                </div>
              </div>
            </div>
            <div className='flex items-start space-x-4 bg-white/80 rounded-xl p-5 border border-lime-100 shadow hover:shadow-lg transition'>
              <Shield className='h-7 w-7 text-lime-700 flex-shrink-0' />
              <div>
                <div className='font-semibold text-gray-900 text-lg'>
                  Data Security & Compliance
                </div>
                <div className='text-gray-700 text-sm'>
                  Enterprise-grade security, traceability, and compliance for
                  global agriculture standards.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className='relative flex min-h-screen items-center justify-center p-4 lg:p-8 overflow-y-auto'>
        <div className='absolute inset-0'>
          {/* Enhanced Multi-Layer Overlay System for Natural Look */}
          <div className='absolute inset-0 bg-radial-gradient from-transparent via-transparent to-black/25' />

          {/* Agricultural Technology Pattern Overlay */}
          <div className='absolute inset-0 opacity-[0.06]'>
            <div
              className='absolute top-0 left-0 w-full h-full'
              style={{
                backgroundImage: `
                  radial-gradient(circle at 15% 25%, #22c55e 1.5px, transparent 1.5px),
                  radial-gradient(circle at 85% 75%, #16a34a 1px, transparent 1px),
                  radial-gradient(circle at 45% 55%, #84cc16 0.8px, transparent 0.8px),
                  radial-gradient(circle at 70% 30%, #15803d 1.2px, transparent 1.2px)
                `,
                backgroundSize: '90px 90px, 70px 70px, 50px 50px, 110px 110px',
              }}
            />
          </div>

          {/* Enhanced Natural Lighting Effects */}
          <div className='absolute top-0 left-0 w-[28rem] h-[28rem] bg-gradient-to-br from-green-400/18 via-emerald-400/12 to-transparent rounded-full blur-3xl animate-pulse' />
          <div className='absolute top-1/4 right-0 w-96 h-96 bg-gradient-to-bl from-lime-400/15 via-green-400/10 to-transparent rounded-full blur-3xl animate-pulse delay-1000' />
          <div className='absolute bottom-0 left-1/3 w-[36rem] h-[36rem] bg-gradient-to-tr from-emerald-500/14 via-green-400/9 to-transparent rounded-full blur-3xl animate-pulse delay-2000' />
          <div className='absolute bottom-1/3 right-1/4 w-80 h-80 bg-gradient-to-tl from-lime-500/12 via-emerald-400/8 to-transparent rounded-full blur-3xl animate-pulse delay-3000' />
          <div className='absolute top-1/2 left-1/2 w-64 h-64 bg-gradient-to-br from-green-300/10 via-lime-300/6 to-transparent rounded-full blur-3xl animate-pulse delay-1500' />
        </div>

        {/* Enhanced Agricultural Technology Floating Elements */}
        <div className='absolute inset-0 overflow-hidden pointer-events-none'>
          {/* Agricultural Technology Icons with Enhanced Visibility */}
          <Leaf className='absolute top-28 left-1/4 w-8 h-8 text-green-300/75 animate-bounce delay-500 drop-shadow-2xl filter brightness-130' />
          <Leaf className='absolute top-44 left-1/3 w-6 h-6 text-emerald-300/70 animate-bounce delay-700 drop-shadow-xl filter brightness-125' />
          <Leaf className='absolute bottom-52 right-1/4 w-7 h-7 text-lime-300/80 animate-bounce delay-2000 drop-shadow-2xl filter brightness-135' />
          <Leaf className='absolute bottom-68 right-1/3 w-5 h-5 text-green-400/75 animate-bounce delay-2500 drop-shadow-xl filter brightness-130' />

          {/* Drone and Technology Icons */}
          <Zap className='absolute top-36 right-1/5 w-6 h-6 text-blue-300/65 animate-bounce delay-600 drop-shadow-xl filter brightness-125' />
          <BarChart3 className='absolute top-76 right-1/3 w-6 h-6 text-cyan-300/60 animate-bounce delay-1000 drop-shadow-xl filter brightness-125' />
          <Activity className='absolute top-100 right-1/4 w-5 h-5 text-indigo-300/65 animate-bounce delay-1200 drop-shadow-xl filter brightness-120' />

          {/* Innovation and Growth Sparkles */}
          <Sparkles className='absolute bottom-100 left-1/3 w-6 h-6 text-lime-300/70 animate-bounce delay-1500 drop-shadow-xl filter brightness-135' />
          <Sparkles className='absolute top-52 left-1/5 w-4 h-4 text-yellow-300/75 animate-bounce delay-800 drop-shadow-xl filter brightness-140' />
          <Sparkles className='absolute bottom-80 right-1/5 w-5 h-5 text-green-300/70 animate-bounce delay-1700 drop-shadow-xl filter brightness-130' />

          {/* Agricultural and Security Icons */}
          <TreePine className='absolute bottom-44 left-1/4 w-5 h-5 text-emerald-300/65 animate-bounce delay-1800 drop-shadow-xl filter brightness-125' />
          <Shield className='absolute top-84 left-1/6 w-5 h-5 text-cyan-300/70 animate-bounce delay-2200 drop-shadow-xl filter brightness-130' />

          {/* Enhanced Glowing Technology Accent Elements */}
          <div className='absolute top-40 right-1/5 w-4 h-4 bg-lime-300/75 rounded-full animate-pulse delay-300 shadow-2xl shadow-lime-300/60 blur-[0.5px]' />
          <div className='absolute bottom-60 left-1/5 w-3 h-3 bg-emerald-300/70 rounded-full animate-pulse delay-1600 shadow-xl shadow-emerald-300/50 blur-[0.5px]' />
          <div className='absolute top-60 right-1/6 w-2 h-2 bg-green-400/65 rounded-full animate-pulse delay-2400 shadow-lg shadow-green-400/45' />
          <div className='absolute bottom-36 right-1/3 w-3 h-3 bg-lime-400/70 rounded-full animate-pulse delay-900 shadow-xl shadow-lime-400/50 blur-[0.5px]' />
        </div>

        <div className='absolute inset-0 opacity-5'>
          <div className='absolute top-20 right-10 w-40 h-40 rounded-full bg-lime-200 blur-3xl animate-pulse delay-1000' />
          <div className='absolute bottom-20 left-10 w-32 h-32 rounded-full bg-green-300 blur-3xl animate-pulse delay-2000' />
        </div>

        <div className='relative z-10 flex w-full max-w-md flex-col items-center justify-center space-y-8 py-8'>
          {/* Enhanced Mobile Logo */}
          <div className='flex items-center space-x-4 lg:hidden group'>
            <div className='w-14 h-14 rounded-2xl bg-gradient-to-br from-green-500 to-emerald-600 flex items-center justify-center shadow-xl shadow-green-200/60 group-hover:scale-105 transition-transform duration-300'>
              <TreePine className='h-8 w-8 text-white' />
            </div>
            <div>
              <span className='text-2xl font-bold text-gray-900'>
                AgriWise ERP
              </span>
              <p className='text-base text-gray-600 font-semibold'>
                Smart Agricultural ERP
              </p>
            </div>
          </div>

          {/* Enhanced Welcome Section */}
          <div className='text-center space-y-6'>
            <div className='space-y-4'>
              <h1 className='text-5xl font-bold text-gray-900 tracking-tight'>
                Welcome back
              </h1>
              <p className='text-gray-600 leading-relaxed text-xl font-medium'>
                AI-driven agriculture. Smarter. Simpler.
              </p>
            </div>

            {/* Enhanced Status Indicator */}
            {/* <div className='inline-flex items-center space-x-3 px-6 py-3 bg-green-100/90 backdrop-blur-sm rounded-full border border-green-200/70 shadow-lg'>
              <div className='w-3 h-3 bg-green-500 rounded-full animate-pulse shadow-lg shadow-green-500/60' />
              <span className='text-base font-bold text-green-700'>
                Smart Agriculture System Active
              </span>
            </div> */}
          </div>

          <UserAuthForm />

          {/* Enhanced Terms Section */}
          <div>
            <p className='text-sm text-gray-600 text-center leading-relaxed font-medium'>
              By signing in, you agree to our{' '}
              <Link
                href='/terms'
                className='text-primary hover:text-green-700 underline underline-offset-2 font-semibold transition-colors duration-200'
              >
                Terms of Service
              </Link>{' '}
              and{' '}
              <Link
                href='/privacy'
                className='text-primary hover:text-green-700 underline underline-offset-2 font-semibold transition-colors duration-200'
              >
                Privacy Policy
              </Link>
            </p>
          </div>

          {/* Enhanced Trust Indicators */}
          <div className='flex items-center justify-center space-x-10 text-base text-gray-500'>
            <div className='flex items-center space-x-3'>
              <Shield className='w-5 h-5 text-green-500' />
              <span className='font-medium'>Enterprise Security</span>
            </div>
            <div className='flex items-center space-x-3'>
              <Zap className='w-5 h-5 text-green-500' />
              <span className='font-medium'>AI-Powered Analytics</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
