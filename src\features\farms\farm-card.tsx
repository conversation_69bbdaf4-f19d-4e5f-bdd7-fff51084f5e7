"use client";

import type React from "react";
import { Badge } from "@/components/ui/badge";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON>ooter,
  CardHeader,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import {
  MapPin,
  TreePine,
  Cpu,
  AlertCircle,
  Activity,
  Leaf,
  Battery,
  Droplets,
} from "lucide-react";
import { useMemo } from "react";
import type { FarmData } from "./types";
import useFarmStore from "./store";
import EnhancedFarmModal from "./farm-analyzed-modal";

type Props = {
  farm: FarmData;
  onSelect: (farm: FarmData) => void;
};

export const FarmStatusColors = {
  good: {
    bg: "bg-green-50 dark:bg-green-950/30",
    border: "border-green-300 dark:border-green-700",
    badge:
      "bg-green-100 text-green-700 border-green-200 dark:bg-green-900/50 dark:text-green-300",
    accent: "text-green-600 dark:text-green-400",
    progress: "bg-green-500",
    title: "text-green-600 dark:text-green-400",
  },
  warning: {
    bg: "bg-amber-50 dark:bg-amber-950/30",
    border: "border-amber-300 dark:border-amber-700",
    badge:
      "bg-amber-100 text-amber-700 border-amber-200 dark:bg-amber-900/50 dark:text-amber-300",
    accent: "text-amber-600 dark:text-amber-400",
    progress: "bg-amber-500",
    title: "text-amber-600 dark:text-amber-400",
  },
  danger: {
    bg: "bg-red-50 dark:bg-red-950/30",
    border: "border-red-300 dark:border-red-700",
    badge:
      "bg-red-100 text-red-700 border-red-200 dark:bg-red-900/50 dark:text-red-300",
    accent: "text-red-600 dark:text-red-400",
    progress: "bg-red-500",
    title: "text-red-600 dark:text-red-400",
  },
};

function FarmCard({ farm, onSelect }: Props) {
  const isSelected = useFarmStore(
    (state) => state.selectedFarm?.id === farm.id
  );

  const healthyRate = useMemo(() => {
    return (
      ((farm.droneData?.healthTree || 0) / (farm.droneData?.totalTree || 1)) *
      100
    );
  }, [farm]);

  const averageBattery = useMemo(() => {
    if (farm.sensors.length === 0) return 0;
    const totalBattery = farm.sensors.reduce(
      (sum, sensor) => sum + (sensor.battery ?? 0),
      0
    );
    return Math.round(totalBattery / farm.sensors.length);
  }, [farm.sensors]);

  const formatLastWatered = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return `${diffDays} days ago`;
  };

  const statusConfig = FarmStatusColors[farm.status];

  return (
    <motion.div
      whileHover={{ scale: 1.02, y: -2 }}
      whileTap={{ scale: 0.98 }}
      transition={{ duration: 0.2, ease: "easeOut" }}
    >
      <Card
        className={cn(
          "cursor-pointer transition-all duration-300 hover:shadow-lg border-2 group relative overflow-hidden",
          statusConfig.bg,
          isSelected
            ? "border-blue-500 shadow-lg shadow-blue-100 dark:shadow-blue-900/20"
            : cn(statusConfig.border, "hover:border-opacity-80")
        )}
        onClick={() => onSelect(farm)}
        role="button"
        tabIndex={0}
        aria-label={`Select ${farm.name} farm`}
        onKeyDown={(e) => {
          if (e.key === "Enter" || e.key === " ") {
            e.preventDefault();
            onSelect(farm);
          }
        }}
      >
        <CardHeader className="pb-4 relative">
          <div className="flex justify-between items-start gap-3">
            <div className="flex-1 min-w-0 pr-2">
              <h3
                className={cn(
                  "font-bold text-xl leading-tight transition-colors",
                  statusConfig.title
                )}
              >
                {farm.name}
              </h3>
              <Badge
                variant="outline"
                className="mt-2 bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/50 dark:text-blue-300 font-medium inline-flex items-center gap-1"
              >
                <Leaf className="h-3 w-3" />
                {farm.variety}
              </Badge>
            </div>
            <div className="flex-shrink-0">
              <Badge
                className={cn(
                  statusConfig.badge,
                  "font-semibold px-3 py-1.5 rounded-full border shadow-sm whitespace-nowrap text-sm"
                )}
              >
                {Math.round(healthyRate)}% Healthy
              </Badge>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4 relative">
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Tree Health
              </span>
              <span className="text-lg font-bold text-gray-900 dark:text-gray-100">
                {Math.round(healthyRate)}%
              </span>
            </div>
            <Progress
              value={healthyRate}
              className="h-2 bg-gray-200 dark:bg-gray-700"
              style={
                {
                  "--progress-background": statusConfig.progress,
                } as React.CSSProperties
              }
            />
          </div>

          {farm.healthAlert && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              className="bg-red-50 dark:bg-red-950/50 border border-red-200 dark:border-red-800 rounded-lg p-3"
            >
              <div className="flex items-start gap-2">
                <AlertCircle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-red-800 dark:text-red-200 mb-1">
                    Health Alert
                  </p>
                  <p className="text-xs text-red-600 dark:text-red-300">
                    <span className="font-medium">Health:</span>{" "}
                    {farm.healthAlert.health}
                  </p>
                  <p className="text-xs text-red-600 dark:text-red-300">
                    <span className="font-medium">Pest:</span>{" "}
                    {farm.healthAlert.pest}
                  </p>
                </div>
              </div>
            </motion.div>
          )}

          <div className="grid grid-cols-2 gap-6">
            <div className="flex items-center gap-2">
              <TreePine className={cn("h-5 w-5", statusConfig.accent)} />
              <div>
                <p className="text-xl font-bold text-gray-900 dark:text-gray-100">
                  {farm.droneData?.totalTree}
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Trees
                </p>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Cpu className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-xl font-bold text-gray-900 dark:text-gray-100">
                  {farm.sensors.length}
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Sensors
                </p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-6">
            <div className="flex items-center gap-2">
              <Activity className="h-4 w-4 text-green-500" />
              <div>
                <p className="text-lg font-bold text-gray-900 dark:text-gray-100">
                  {farm.droneData.canopyCoverage}%
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Coverage
                </p>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Battery className="h-4 w-4 text-blue-500" />
              <div>
                <p className="text-lg font-bold text-gray-900 dark:text-gray-100">
                  {averageBattery}%
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Avg Battery
                </p>
              </div>
            </div>
          </div>

          <div className="flex items-start gap-2 pt-2">
            <MapPin className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
            <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
              {farm.address}
            </p>
          </div>

          <div className="flex items-center gap-2">
            <Droplets className="h-4 w-4 text-blue-500 flex-shrink-0" />
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Last watered:{" "}
              <span className="font-medium">
                {formatLastWatered(farm.lastWatered)}
              </span>
            </p>
          </div>
        </CardContent>

        <CardFooter className="pt-0 relative">
          <EnhancedFarmModal farm={farm} />
        </CardFooter>

        {isSelected && (
          <motion.div
            initial={{ scaleX: 0 }}
            animate={{ scaleX: 1 }}
            className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-400 to-blue-600"
          />
        )}
      </Card>
    </motion.div>
  );
}

export default FarmCard;
