'use client';

import { Invoice } from "@/constants/invoice/mock-api";
import { InvoiceTable } from "./invoice-tables";
import { columns } from "./invoice-tables/columns";
import { invoiceApi } from "@/lib/invoice-api";
import { useCallback, useEffect, useMemo, useState } from "react";
import { parseAsInteger, parseAsString, useQueryState } from "nuqs";
import { getSortingStateParser } from "@/lib/parsers";
import type { ExtendedColumnSort } from "@/types/data-table";
import { DataTableSkeleton } from "@/components/ui/table/data-table-skeleton";
// Convert sorting state to order_by format for API
const getOrderBy = (sortingState: ExtendedColumnSort<Invoice>[]): string => {
  if (!sortingState || sortingState.length === 0) {
    return "due_date desc"; // Default sorting
  }

  const sortItem = sortingState[0]; // Take first sort item
  const direction = sortItem.desc ? "desc" : "asc";
  return `${sortItem.id} ${direction}`;
};
type InvoiceListingPageProps = {};

export default function InvoiceListingPage({}: InvoiceListingPageProps) {
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [totalInvoices, setTotalInvoices] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [newInvoiceIds, setNewInvoiceIds] = useState<Set<string>>(new Set());

  // Get search params using nuqs hooks
  const [page] = useQueryState("page", parseAsInteger.withDefault(1));
  const [search] = useQueryState("name", parseAsString.withDefault(""));
  const [pageLimit] = useQueryState("perPage", parseAsInteger.withDefault(10));
  const [categories] = useQueryState("category", parseAsString.withDefault(""));

  // Get column IDs for sorting validation
  const columnIds = new Set(columns.map(column => {
    if (column.id) return column.id;
    if ('accessorKey' in column) return column.accessorKey as string;
    return null;
  }).filter(Boolean) as string[]);

  // Get sorting state from URL
  const [sorting] = useQueryState(
    "sort",
    getSortingStateParser<Invoice>(columnIds).withDefault([])
  );

  const getOrderByString = useMemo(() => {
    return getOrderBy(sorting);
  }, [sorting.length ? sorting : null]);

  const fetchInvoices = useCallback(async (isRefresh = false) => {
    try {
      setError(null);
      if (!isRefresh) {
        setIsLoading(true);
      }

      const filters = {
        page,
        limit: pageLimit,
        ...(search && { search }),
        ...(categories && { suppliers: categories }),
        order_by: getOrderByString
      };

      const data = await invoiceApi.getInvoices(filters);

      // Detect new invoices for animation only on refresh
      if (isRefresh && invoices.length > 0) {
        const currentInvoiceIds = new Set(invoices.map(inv => inv.id));
        const newIds = new Set<string>();

        data.invoices.forEach(invoice => {
          if (!currentInvoiceIds.has(invoice.id)) {
            newIds.add(invoice.id);
          }
        });

        setNewInvoiceIds(newIds);

        // Clear new invoice indicators after animation
        if (newIds.size > 0) {
          setTimeout(() => {
            setNewInvoiceIds(new Set());
          }, 3000);
        }
      }

      setInvoices(data.invoices);
      setTotalInvoices(data.total_invoices);
    } catch (error) {
      console.error('Error fetching invoices:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch invoices');
    } finally {
      setIsLoading(false);
    }
  }, [page, search, pageLimit, categories, invoices, getOrderByString]);

  // Refresh function for auto-refresh
  const handleRefresh = useCallback(() => {
    return fetchInvoices(true);
  }, [fetchInvoices]);

  // Initial load
  useEffect(() => {
    fetchInvoices(false);
  }, [page, search, pageLimit, categories, getOrderByString]);

  if (isLoading) {
    return <DataTableSkeleton columnCount={5} rowCount={8} filterCount={2} />;
  }

  if (error) {
    throw new Error(error);
  }

  return (
    <InvoiceTable
      data={invoices}
      totalItems={totalInvoices}
      columns={columns}
      newInvoiceIds={newInvoiceIds}
      onRefresh={handleRefresh}
    />
  );
}
