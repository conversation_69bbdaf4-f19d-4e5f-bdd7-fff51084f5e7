import { useMemo } from "react";
import { Icon } from "@tabler/icons-react";

interface IconProps {
  size?: number;
  stroke?: string;
  strokeWidth?: number;
}

export const useTablerIcon = (
  IconComponent: Icon,
  color: string = "currentColor",
  size: number = 24,
  strokeWidth: number = 2
): string => {
  return useMemo(() => {
    if (!IconComponent) return "";

    // Create SVG manually for data URL
    const createSvgDataUrl = (iconNameParam: string): string => {
      const iconPaths: Record<string, string> = {
        IconMessageCircle: `
          <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
          <path d="M3 20l1.3 -3.9c-2.324 -3.437 -1.426 -7.872 2.1 -10.374c3.526 -2.501 8.59 -2.296 11.845 .48c3.255 2.777 3.695 7.266 1.029 10.501c-2.666 3.235 -7.615 4.215 -11.574 2.293l-4.7 1" />
        `,
        IconUser: `
          <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
          <path d="M8 7a4 4 0 1 0 8 0a4 4 0 0 0 -8 0" />
          <path d="M6 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2" />
        `,
        IconRobot: `
          <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
          <path d="M7 7h10a2 2 0 0 1 2 2v1l1 1v3l-1 1v3a2 2 0 0 1 -2 2h-10a2 2 0 0 1 -2 -2v-3l-1 -1v-3l1 -1v-1a2 2 0 0 1 2 -2z" />
          <path d="M10 16h4" />
          <circle cx="8.5" cy="11.5" r=".5" fill="currentColor" />
          <circle cx="15.5" cy="11.5" r=".5" fill="currentColor" />
          <path d="M9 7l-1 -4" />
          <path d="M15 7l1 -4" />
        `,
        IconSend: `
          <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
          <path d="L10 14l11 -11" />
          <path d="M21 3l-6.5 18a.55 .55 0 0 1 -1 0l-3.5 -7l-7 -3.5a.55 .55 0 0 1 0 -1l18 -6.5" />
        `,
        IconMessage: `
          <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
          <path d="M8 9h8" />
          <path d="M8 13h6" />
          <path d="M18 4a3 3 0 0 1 3 3v8a3 3 0 0 1 -3 3h-5l-5 3v-3h-2a3 3 0 0 1 -3 -3v-8a3 3 0 0 1 3 -3h12z" />
        `,
      };

      const iconName =
        IconComponent.displayName || IconComponent.name || "IconMessage";
      const pathData = iconPaths[iconNameParam] || iconPaths["IconMessage"];

      const svg = `
        <svg xmlns="http://www.w3.org/2000/svg" width="${size}" height="${size}" viewBox="0 0 24 24" fill="none" stroke="${color}" stroke-width="${strokeWidth}" stroke-linecap="round" stroke-linejoin="round" class="tabler-icon">
          ${pathData}
        </svg>
      `;

      return `data:image/svg+xml;base64,${btoa(svg)}`;
    };

    const iconName =
      IconComponent.displayName || IconComponent.name || "IconMessage";
    return createSvgDataUrl(iconName);
  }, [IconComponent, color, size, strokeWidth]);
};
