import js from '@eslint/js';
import next from 'eslint-plugin-next';
import tseslint from 'typescript-eslint';

export default [
  js.configs.recommended,

  ...tseslint.configs.recommendedTypeChecked,

  {
    files: ['**/*.ts', '**/*.tsx'],
    languageOptions: {
      parser: tseslint.parser,
      parserOptions: {
        project: './tsconfig.json',
        tsconfigRootDir: process.cwd(),
        ecmaVersion: 'latest',
        sourceType: 'module',
        ecmaFeatures: { jsx: true },
      }
    },
    plugins: {
      '@typescript-eslint': tseslint.plugin,
      next: next
    },
    rules: {
      '@typescript-eslint/no-unused-vars': 'warn',
      '@typescript-eslint/ban-types': 'warn',
      '@typescript-eslint/ban-ts-comment': 'warn',
      '@typescript-eslint/no-explicit-any': 'warn',
      'react/no-unescaped-entities': 'warn',
      // 'react-hooks/exhaustive-deps': 'warn',
    }
  },

  {
    files: ['**/*.js', '**/*.jsx'],
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module',
      ecmaFeatures: { jsx: true },
    }
  },

  {
    files: ['**/*.{js,jsx,ts,tsx}'],
    plugins: { next },
    settings: {
      next: {
        rootDir: './'
      }
    },
    rules: next.configs['core-web-vitals'].rules
  }
];
