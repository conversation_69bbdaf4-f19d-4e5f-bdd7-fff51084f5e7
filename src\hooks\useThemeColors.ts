import { useState, useEffect } from "react";

interface ThemeColors {
  primary: string;
  primaryForeground: string;
}

export const useThemeColors = (): ThemeColors => {
  const [colors, setColors] = useState<ThemeColors>({
    primary: "#3B81F6",
    primaryForeground: "#ffffff",
  });

  useEffect(() => {
    const updateColors = (): void => {
      const computedStyle = getComputedStyle(document.documentElement);
      const primary = computedStyle.getPropertyValue("--primary").trim();
      const primaryForeground = computedStyle
        .getPropertyValue("--primary-foreground")
        .trim();

      if (primary && primaryForeground) {
        // Convert oklch/rgb to hex if needed
        const primaryColor = convertColorToHex(primary) || "#3B81F6";
        const foregroundColor =
          convertColorToHex(primaryForeground) || "#ffffff";

        setColors({
          primary: primaryColor,
          primaryForeground: foregroundColor,
        });
      }
    };

    updateColors();

    // Watch for theme changes
    const observer = new MutationObserver(() => {
      updateColors();
    });

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ["class"],
    });

    return () => observer.disconnect();
  }, []);

  return colors;
};

// Helper function to convert various color formats to hex
const convertColorToHex = (color: string): string => {
  if (!color) return "";

  // If already hex
  if (color.startsWith("#")) return color;

  // If RGB
  const rgbMatch = color.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
  if (rgbMatch) {
    const r = parseInt(rgbMatch[1]);
    const g = parseInt(rgbMatch[2]);
    const b = parseInt(rgbMatch[3]);
    return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
  }

  // If OKLCH (approximation for common values)
  if (color.includes("oklch")) {
    // Common theme color mappings
    const colorMap: Record<string, string> = {
      "oklch(0.646 0.222 41.116)": "#3B81F6", // blue
      "oklch(0.769 0.188 70.08)": "#84CC16", // lime/green
      "oklch(0.828 0.189 84.429)": "#F59E0B", // amber
      "oklch(0.205 0 0)": "#374151", // neutral
      "oklch(0.985 0 0)": "#ffffff", // white
      "oklch(0.145 0 0)": "#1F2937", // dark
    };

    return colorMap[color] || "#3B81F6";
  }

  return color;
};
