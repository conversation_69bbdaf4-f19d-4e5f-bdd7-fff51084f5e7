'use client';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function Page() {
  const router = useRouter();
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const isAuthenticated = localStorage.getItem('isAuthenticated');
      if (isAuthenticated === 'true') {
        router.replace('/dashboard/overview');
      } else {
        router.replace('/auth/sign-in');
      }
    }
  }, [router]);
  return null;
}
