'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { Invoice } from '@/constants/invoice/mock-api';
import { invoiceApi } from '@/lib/invoice-api';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Building2,
  User,
  FileText,
  Package,
  DollarSign,
  Clock,
  MessageSquare,
} from 'lucide-react';

// Skeleton component for loading state
function InvoiceSkeleton() {
  return (
    <div className='bg-gray-50 min-h-screen overflow-auto animate-pulse'>
      <div className='max-w-7xl mx-auto p-4 md:p-6 space-y-6 pb-8'>
        <div className='h-10 w-1/3 bg-gray-200 rounded mb-4' />
        <div className='flex gap-2 mb-6'>
          <div className='h-10 w-32 bg-gray-200 rounded' />
          <div className='h-10 w-32 bg-gray-200 rounded' />
        </div>
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6'>
          {[...Array(4)].map((_, i) => (
            <div key={i} className='h-24 bg-gray-200 rounded' />
          ))}
        </div>
        <div className='grid grid-cols-1 lg:grid-cols-3 gap-6 items-start'>
          <div className='lg:col-span-2 space-y-6'>
            {[...Array(3)].map((_, i) => (
              <div key={i} className='h-40 bg-gray-200 rounded' />
            ))}
          </div>
          <div className='space-y-6'>
            {[...Array(3)].map((_, i) => (
              <div key={i} className='h-32 bg-gray-200 rounded' />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

export default function EnhancedInvoiceDetail() {
  const { invoiceId } = useParams();
  const [invoiceData, setInvoiceData] = useState<Invoice | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!invoiceId) return;
    setLoading(true);
    invoiceApi
      .getInvoiceById(invoiceId as string)
      .then(res => {
        if (res.success && res.invoice) {
          setInvoiceData(res.invoice as Invoice);
          setError(null);
        } else {
          setError(res.message);
          setInvoiceData(null);
        }
      })
      .catch((error) => {
        setError('Failed to fetch invoice');
        setInvoiceData(null);
        console.error('Error fetching invoice:', error);
      })
      .finally(() => setLoading(false));
  }, [invoiceId]);

  const formatCurrency = (amount: number) => {
    if (!invoiceData) return '-';
    return new Intl.NumberFormat('th-TH', {
      style: 'currency',
      currency: invoiceData.currency,
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('th-TH').format(num);
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'draft':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'submitted':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'paid':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'unpaid':
        return 'bg-amber-100 text-amber-800 border-amber-200';
      case 'overdue':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (loading) return <InvoiceSkeleton />;
  if (error || !invoiceData)
    return (
      <div className='p-8 text-center text-red-500'>
        {error || 'Invoice not found'}
      </div>
    );

  return (
    <div className='bg-gray-50 min-h-screen overflow-auto'>
      <div className='max-w-7xl mx-auto p-4 md:p-6 space-y-6 pb-8'>
        {/* Header */}
        <div className='flex flex-col md:flex-row md:items-center md:justify-between gap-4'>
          <div>
            <h1 className='text-3xl font-bold text-gray-900'>
              Invoice Details
            </h1>
            <p className='text-gray-600 mt-1'>Invoice #{invoiceData.bill_no}</p>
          </div>
          <div className='flex gap-2'>
            <Button variant='outline'>
              <FileText className='w-4 h-4 mr-2' />
              Download PDF
            </Button>
            <Button>Edit Invoice</Button>
          </div>
        </div>

        <Tabs defaultValue='details' className='space-y-6'>
          <TabsList className='grid w-full grid-cols-5 lg:w-auto lg:grid-cols-5'>
            <TabsTrigger value='details'>Details</TabsTrigger>
            <TabsTrigger value='payments'>Payments</TabsTrigger>
            <TabsTrigger value='terms'>Terms</TabsTrigger>
            <TabsTrigger value='activity'>Activity</TabsTrigger>
            <TabsTrigger value='comments'>Comments</TabsTrigger>
          </TabsList>

          <TabsContent value='details' className='space-y-6'>
            <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
              <Card>
                <CardContent className='p-4'>
                  <div className='flex items-center gap-3'>
                    <div className='p-2 bg-blue-100 rounded-lg'>
                      <Building2 className='w-5 h-5 text-blue-600' />
                    </div>
                    <div>
                      <p className='text-sm text-gray-600'>Supplier</p>
                      <p className='font-semibold text-gray-900'>
                        {invoiceData.supplier}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className='p-4'>
                  <div className='flex items-center gap-3'>
                    <div className='p-2 bg-green-100 rounded-lg'>
                      <DollarSign className='w-5 h-5 text-green-600' />
                    </div>
                    <div>
                      <p className='text-sm text-gray-600'>Grand Total</p>
                      <p className='font-semibold text-gray-900'>
                        {formatCurrency(invoiceData.grand_total)}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className='p-4'>
                  <div className='flex items-center gap-3'>
                    <div className='p-2 bg-orange-100 rounded-lg'>
                      <Clock className='w-5 h-5 text-orange-600' />
                    </div>
                    <div>
                      <p className='text-sm text-gray-600'>Due Date</p>
                      <p className='font-semibold text-gray-900'>
                        {invoiceData.due_date}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className='p-4'>
                  <div className='flex items-center gap-3'>
                    <div className='p-2 bg-purple-100 rounded-lg'>
                      <Package className='w-5 h-5 text-purple-600' />
                    </div>
                    <div>
                      <p className='text-sm text-gray-600'>Total Items</p>
                      <p className='font-semibold text-gray-900'>
                        {invoiceData.items.length}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Main Content */}
            <div className='grid grid-cols-1 lg:grid-cols-3 gap-6 items-start'>
              <div className='lg:col-span-2 space-y-6'>
                <Card>
                  <CardHeader>
                    <CardTitle className='flex items-center gap-2'>
                      <FileText className='w-5 h-5' />
                      Invoice Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className='space-y-4'>
                    <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                      <div className='space-y-3'>
                        <div>
                          <label className='text-sm font-medium text-gray-600'>
                            Invoice Number
                          </label>
                          <p className='text-gray-900 font-medium'>
                            {invoiceData.bill_no}
                          </p>
                        </div>
                        <div>
                          <label className='text-sm font-medium text-gray-600'>
                            Posting Date
                          </label>
                          <p className='text-gray-900'>
                            {invoiceData.posting_date}
                          </p>
                        </div>
                        <div>
                          <label className='text-sm font-medium text-gray-600'>
                            Company
                          </label>
                          <p className='text-gray-900'>{invoiceData.company}</p>
                        </div>
                      </div>
                      <div className='space-y-3'>
                        <div>
                          <label className='text-sm font-medium text-gray-600'>
                            Status
                          </label>
                          <div className='mt-1'>
                            <Badge
                              className={getStatusColor(invoiceData.status)}
                            >
                              {invoiceData.status}
                            </Badge>
                          </div>
                        </div>
                        <div>
                          <label className='text-sm font-medium text-gray-600'>
                            Due Date
                          </label>
                          <p className='text-gray-900'>
                            {invoiceData.due_date}
                          </p>
                        </div>
                        <div>
                          <label className='text-sm font-medium text-gray-600'>
                            Cost Center
                          </label>
                          <p className='text-gray-900'>
                            {invoiceData.cost_center}
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className='flex items-center gap-2'>
                      <User className='w-5 h-5' />
                      Supplier Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className='space-y-2'>
                      <div>
                        <label className='text-sm font-medium text-gray-600'>
                          Supplier Name
                        </label>
                        <p className='text-gray-900 font-medium'>
                          {invoiceData.supplier}
                        </p>
                      </div>
                      <div>
                        <label className='text-sm font-medium text-gray-600'>
                          Address
                        </label>
                        <p className='text-gray-900'>
                          {invoiceData.supplier_address}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className='flex items-center gap-2'>
                      <Package className='w-5 h-5' />
                      Items ({invoiceData.items.length})
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className='overflow-x-auto'>
                      <table className='w-full'>
                        <thead>
                          <tr className='border-b border-gray-200'>
                            <th className='text-left py-3 px-2 font-medium text-gray-600'>
                              #
                            </th>
                            <th className='text-left py-3 px-2 font-medium text-gray-600'>
                              Item
                            </th>
                            <th className='text-left py-3 px-2 font-medium text-gray-600'>
                              Description
                            </th>
                            <th className='text-right py-3 px-2 font-medium text-gray-600'>
                              Qty
                            </th>
                            <th className='text-right py-3 px-2 font-medium text-gray-600'>
                              Rate
                            </th>
                            <th className='text-right py-3 px-2 font-medium text-gray-600'>
                              Amount
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {invoiceData.items.map((item, index) => (
                            <tr
                              key={item.item_code}
                              className='border-b border-gray-100 hover:bg-gray-50'
                            >
                              <td className='py-3 px-2 text-gray-600'>
                                {index + 1}
                              </td>
                              <td className='py-3 px-2'>
                                <div>
                                  <p className='font-medium text-gray-900'>
                                    {item.item_name}
                                  </p>
                                  <p className='text-sm text-gray-500'>
                                    {item.item_code}
                                  </p>
                                </div>
                              </td>
                              <td className='py-3 px-2 text-gray-600 max-w-xs'>
                                <p className='text-sm'>{item.description}</p>
                                <p className='text-xs text-gray-500 mt-1'>
                                  UOM: {item.uom}
                                </p>
                              </td>
                              <td className='py-3 px-2 text-right font-medium'>
                                {formatNumber(item.qty)}
                              </td>
                              <td className='py-3 px-2 text-right'>
                                {formatCurrency(item.rate)}
                              </td>
                              <td className='py-3 px-2 text-right font-medium'>
                                {formatCurrency(item.amount)}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </CardContent>
                </Card>

                {invoiceData.remarks && (
                  <Card>
                    <CardHeader>
                      <CardTitle className='flex items-center gap-2'>
                        <MessageSquare className='w-5 h-5' />
                        Remarks
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className='text-gray-700 bg-gray-50 p-3 rounded-lg'>
                        {invoiceData.remarks}
                      </p>
                    </CardContent>
                  </Card>
                )}
              </div>

              <div className='space-y-6'>
                <Card>
                  <CardHeader>
                    <CardTitle>Financial Summary</CardTitle>
                  </CardHeader>
                  <CardContent className='space-y-4'>
                    <div className='space-y-3'>
                      <div className='flex justify-between'>
                        <span className='text-gray-600'>Total Quantity:</span>
                        <span className='font-medium'>
                          {formatNumber(invoiceData.total_qty)} Kg
                        </span>
                      </div>
                      <div className='flex justify-between'>
                        <span className='text-gray-600'>Net Total:</span>
                        <span className='font-medium'>
                          {formatCurrency(invoiceData.net_total)}
                        </span>
                      </div>
                      <div className='flex justify-between'>
                        <span className='text-gray-600'>Total Taxes:</span>
                        <span className='font-medium'>
                          {formatCurrency(invoiceData.total_taxes_and_charges)}
                        </span>
                      </div>
                      <Separator />
                      <div className='flex justify-between text-lg'>
                        <span className='font-semibold'>Grand Total:</span>
                        <span className='font-bold text-green-600'>
                          {formatCurrency(invoiceData.grand_total)}
                        </span>
                      </div>
                      <div className='flex justify-between text-lg'>
                        <span className='font-semibold'>Outstanding:</span>
                        <span className='font-bold text-orange-600'>
                          {formatCurrency(invoiceData.outstanding_amount)}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Currency Details</CardTitle>
                  </CardHeader>
                  <CardContent className='space-y-3'>
                    <div className='flex justify-between'>
                      <span className='text-gray-600'>Currency:</span>
                      <span className='font-medium'>
                        {invoiceData.currency}
                      </span>
                    </div>
                    <div className='flex justify-between'>
                      <span className='text-gray-600'>Conversion Rate:</span>
                      <span className='font-medium'>
                        {invoiceData.conversion_rate}
                      </span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Tax Details</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className='space-y-3'>
                      <div>
                        <label className='text-sm font-medium text-gray-600'>
                          Tax Template
                        </label>
                        <p className='text-gray-900'>
                          {invoiceData.taxes_and_charges}
                        </p>
                      </div>
                      {invoiceData.taxes.map((tax, index) => (
                        <div key={index} className='bg-gray-50 p-3 rounded-lg'>
                          <div className='flex justify-between items-center'>
                            <span className='text-sm text-gray-600'>
                              {tax.account_head}
                            </span>
                            <span className='font-medium'>{tax.rate}%</span>
                          </div>
                          <div className='flex justify-between items-center mt-1'>
                            <span className='text-sm text-gray-600'>
                              Tax Amount:
                            </span>
                            <span className='font-medium'>
                              {formatCurrency(tax.tax_amount)}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value='payments'>
            <Card>
              <CardHeader>
                <CardTitle>Payment Information</CardTitle>
              </CardHeader>
              <CardContent>
                <p className='text-gray-600'>
                  No payment records found for this invoice.
                </p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value='terms'>
            <Card>
              <CardHeader>
                <CardTitle>Terms & Conditions</CardTitle>
              </CardHeader>
              <CardContent>
                <p className='text-gray-600'>
                  No specific terms and conditions defined for this invoice.
                </p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value='activity'>
            <Card>
              <CardHeader>
                <CardTitle>Activity Log</CardTitle>
              </CardHeader>
              <CardContent>
                <div className='space-y-4'>
                  <div className='flex items-start gap-3 p-3 bg-gray-50 rounded-lg'>
                    <div className='w-2 h-2 bg-blue-500 rounded-full mt-2'></div>
                    <div>
                      <p className='text-sm font-medium'>Invoice created</p>
                      <p className='text-xs text-gray-500'>
                        Created on {invoiceData.posting_date}
                      </p>
                    </div>
                  </div>
                  <div className='flex items-start gap-3 p-3 bg-gray-50 rounded-lg'>
                    <div className='w-2 h-2 bg-yellow-500 rounded-full mt-2'></div>
                    <div>
                      <p className='text-sm font-medium'>
                        Status set to {invoiceData.status}
                      </p>
                      <p className='text-xs text-gray-500'>
                        Current status: {invoiceData.status}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value='comments'>
            <Card>
              <CardHeader>
                <CardTitle>Comments</CardTitle>
              </CardHeader>
              <CardContent>
                <p className='text-gray-600'>
                  No comments available for this invoice.
                </p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
