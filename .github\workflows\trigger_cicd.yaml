# name: Trigger Common Build Workflow
# on:
#   push:
#     branches: [ main ]

#   release:
#     types: [published]
  
#   workflow_dispatch:
#     inputs:
#       Environment:
#         description: 'The environment need to be deploy'
#         required: true
#         default: staging
#         type: choice
#         options:
#           - staging
#           - prod

# jobs:
#   Verify-For-Release:
#     runs-on: ubuntu-latest
#     if: github.event_name == 'release' && startsWith(github.event.release.target_commitish, 'release') || github.event_name == 'workflow_dispatch'
#     steps:
#       - name: Get trigger user permission
#         uses: octokit/request-action@v2.4.0
#         with:
#           route: GET /repos/${{ github.repository }}/collaborators/${{ github.actor }}/permission
#         id: check_permission
#         env:
#           GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      
#       # - name: Verify trigger user permission
#       #   if: steps.check_permission.conclusion == 'success'
#       #   id: verify_permission
#       #   run: |
#       #     USER_PERMISSION="${{ from<PERSON><PERSON>(steps.check_permission.outputs.data).permission }}"
#       #     echo "User permission: $USER_PERMISSION"
          
#       #     if [[ "$USER_PERMISSION" != "admin" && "$USER_PERMISSION" != "maintain" ]]; then
#       #       echo "Trigger user does not have admin or maintain permission!"
#       #       exit 1
#       #     fi

#       - name: Validate Semantic Version
#         # if: steps.verify_permission.conclusion == 'success'
#         uses: nowsprinting/check-version-format-action@v4
#         id: version_check
#         with:
#           prefix: 'v'
      
#       - name: Semantic validation result
#         if: steps.version_check.conclusion == 'success'
#         run: |
#           if [ "${{ steps.version_check.outputs.is_valid }}" == "true" ]; then
#             echo "Found valid version format in tag!"
#             echo "Full version: ${{ steps.version_check.outputs.full }}"
#             echo "Major version: ${{ steps.version_check.outputs.major }}"
#             echo "Major with pre-release: ${{ steps.version_check.outputs.major_prerelease }}"
#           else
#             echo "Invalid version format in tag!"
#           fi
#     outputs:
#       valid_version: ${{ steps.version_check.outputs.is_valid }}
    
#   Trigger-CICD:
#     runs-on: ubuntu-latest
#     needs: Verify-For-Release
#     if: |
#       success() || (
#         github.event_name == 'push' &&
#         github.ref == 'refs/heads/main'
#       ) || (
#         github.event_name == 'workflow_dispatch'
#       )
#     env:
#       DISPATCH_REPO: Vietnam-Silicon/cicd-workflows
#       DISPATCH_WORKFLOW: cicd_backend.yml
    
#     steps:
#       - name: Set Environment
#         id: set_environment
#         run: |
#           echo "Triggering the workflow"
#           if [ "${{ github.ref_name }}" == "main" ]; then
#             echo "ENVIRONMENT=dev" >> $GITHUB_ENV
#           elif [ "${{ startsWith(github.event.release.target_commitish, 'release') }}" == "true" ]; then
#             echo "ENVIRONMENT=staging" >> $GITHUB_ENV
#           elif [ "${{ github.event_name }}" == "workflow_dispatch" ]; then
#             if [ "${{ inputs.Environment }}" == "staging" ]; then
#               echo "ENVIRONMENT=staging" >> $GITHUB_ENV
#             elif [ "${{ inputs.Environment }}" == "prod" ]; then
#               echo "ENVIRONMENT=prod" >> $GITHUB_ENV
#             fi
#           fi
      
#       - name: Set version to deploy
#         if: |
#           github.event_name == 'release' && startsWith(github.event.release.target_commitish, 'release') ||
#           github.event_name == 'workflow_dispatch' && startsWith(github.ref, 'release') ||
#           github.event_name == 'workflow_dispatch' && startsWith(github.ref, 'refs/tags/') ||
#           github.event_name == 'workflow_dispatch' && github.ref == 'refs/heads/main' ||
#           github.event_name == 'push' && github.ref == 'refs/heads/main'
#         id: set_version
#         run: |
#           if [ "${{ github.event_name }}" == "workflow_dispatch" ]; then
#             echo "VERSION=${{ github.ref_name }}" >> $GITHUB_ENV
#           elif [ "${{ github.event_name }}" == "push" ]; then
#             echo "VERSION=${{ github.sha }}" >> $GITHUB_ENV
#           else
#             echo "VERSION=${{ github.event.release.tag_name }}" >> $GITHUB_ENV
#           fi

#       - name: Trigger repository dispatch
#         uses: peter-evans/repository-dispatch@v3
#         if: steps.set_environment.conclusion == 'success'
#         with:
#           token: ${{ secrets.GHA_CICD_TOKEN }}
#           repository: Vietnam-Silicon/cicd-workflows
#           event-type: cicd-backend
#           client-payload: '{"triggered_by": "${{ github.repository }}", "commit_sha": "${{ github.sha }}", "branch": "${{ github.event.release.target_commitish }}", 
#                             "project": "${{ vars.PROJECT }}", "service": "${{ vars.SERVICE_NAME }}", "environment": "${{ env.ENVIRONMENT }}",
#                             "full_svc_name": "${{ vars.PROJECT }}-${{ env.ENVIRONMENT }}-${{ vars.SERVICE_NAME }}", "version": "${{ env.VERSION }}"}'

#       - name: Wait for CICD Workflow to start
#         if: steps.set_environment.conclusion == 'success'
#         run: |
#           echo "Waiting for CICD Workflow to start..."
#           sleep 10
    
#       - name: Get Workflow Run Info and Monitor Status
#         if: steps.set_environment.conclusion == 'success'
#         id: monitor
#         run: |
#           echo "Fetching latest run of CICD Workflow...."

#           # Call GitHub API to get the most recent run
#           response=$(curl -s -H "Authorization: Bearer ${{ secrets.GHA_CICD_TOKEN }}" \
#           https://api.github.com/repos/${{ env.DISPATCH_REPO }}/actions/workflows/${{ env.DISPATCH_WORKFLOW }}/runs?branch=main&event=repository_dispatch)

#           # Use jq to parse the first workflow run
#           first_run=$(echo "$response" | jq -r '.workflow_runs[0]')
          
#           # Extract run details using jq
#           run_id=$(echo "$first_run" | jq -r '.id')
#           run_number=$(echo "$first_run" | jq -r '.run_number')
#           html_url=$(echo "$first_run" | jq -r '.html_url')

#           echo "CICD Workflow Run ID: $run_id"
#           echo "CICD Workflow Run Number: $run_number"
#           echo "CICD Workflow URL: $html_url"

#           echo "RUN_ID=$run_id" >> $GITHUB_ENV
#           echo "RUN_URL=$html_url" >> $GITHUB_ENV

#       - name: Generate Workflow Summary
#         if: always()
#         run: |
#           echo "## Workflow Trigger Summary" >> $GITHUB_STEP_SUMMARY
#           echo "### Triggered Workflow Details" >> $GITHUB_STEP_SUMMARY
#           echo "- **Repository:** ${{ env.DISPATCH_REPO }}" >> $GITHUB_STEP_SUMMARY
#           echo "- **Workflow:** ${{ env.DISPATCH_WORKFLOW }}" >> $GITHUB_STEP_SUMMARY
#           echo "- **Run ID:** ${{ env.RUN_ID }}" >> $GITHUB_STEP_SUMMARY
#           echo "- **Run URL:** [${{ env.RUN_URL }}](${{ env.RUN_URL }})" >> $GITHUB_STEP_SUMMARY
#           echo "" >> $GITHUB_STEP_SUMMARY
#           echo "### Trigger Details" >> $GITHUB_STEP_SUMMARY
#           echo "- **Triggered by:** ${{ github.repository }}" >> $GITHUB_STEP_SUMMARY
#           echo "- **Commit SHA:** ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
#           echo "- **Branch:** ${{ github.ref_name }}" >> $GITHUB_STEP_SUMMARY
#           echo "" >> $GITHUB_STEP_SUMMARY