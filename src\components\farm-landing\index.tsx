'use client';

import type React from 'react';
import { useState, useEffect } from 'react';
import {
  Eye,
  EyeOff,
  X,
  AlertTriangle,
  TrendingUp,
  Droplets,
  Thermometer,
  Wind,
  Sun,
  Activity,
  BarChart3,
  Zap,
  Shield,
  Leaf,
} from 'lucide-react';
import { useRouter } from 'next/navigation';

export function ThailandFarm() {
  const [showLogin, setShowLogin] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [realTimeData, setRealTimeData] = useState({
    temperature: 28.5,
    humidity: 65,
    soilMoisture: 72,
    windSpeed: 12,
    uvIndex: 7.2,
    lastUpdated: new Date().toLocaleTimeString(),
  });
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const router = useRouter();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      setRealTimeData(prev => ({
        temperature: prev.temperature + (Math.random() - 0.5) * 2,
        humidity: Math.max(
          30,
          Math.min(90, prev.humidity + (Math.random() - 0.5) * 5)
        ),
        soilMoisture: Math.max(
          20,
          Math.min(100, prev.soilMoisture + (Math.random() - 0.5) * 3)
        ),
        windSpeed: Math.max(0, prev.windSpeed + (Math.random() - 0.5) * 4),
        uvIndex: Math.max(
          0,
          Math.min(12, prev.uvIndex + (Math.random() - 0.5) * 0.5)
        ),
        lastUpdated: new Date().toLocaleTimeString(),
      }));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    setTimeout(() => {
      setIsLoading(false);
      if (typeof window !== 'undefined') {
        localStorage.setItem('isAuthenticated', 'true');
      }
      router.push('/dashboard/overview');
      setShowLogin(false);
      setFormData({ email: '', password: '' });
    }, 2000);
  };

  const stats = [
    {
      value: '500+',
      label: 'Agricultural Products',
      icon: '/images/icons/products-icon.png',
      trend: '+8%',
    },
    {
      value: '25+',
      label: 'Awards Received',
      icon: '/images/icons/awards-icon.png',
      trend: '+3%',
    },
    {
      value: '35%',
      label: 'Annual Growth',
      icon: '/images/icons/growth-icon.png',
      trend: '+5%',
    },
  ];

  const challenges = [
    {
      id: 1,
      title: 'Pest Detection Alert',
      severity: 'high',
      description: 'Brown planthopper detected in rice fields',
      solution: 'Apply targeted pesticide treatment',
      status: 'active',
      impact: '15% yield loss potential',
    },
    {
      id: 2,
      title: 'Irrigation Optimization',
      severity: 'medium',
      description: 'Soil moisture levels below optimal range',
      solution: 'Increase irrigation frequency by 20%',
      status: 'monitoring',
      impact: '8% yield improvement potential',
    },
    {
      id: 3,
      title: 'Nutrient Deficiency',
      severity: 'low',
      description: 'Nitrogen levels slightly below recommended',
      solution: 'Apply organic fertilizer next week',
      status: 'scheduled',
      impact: '5% quality improvement',
    },
  ];

  const products = [
    {
      name: 'AgriDrone Pro X1',
      price: '$12,500',
      organic: true,
      category: 'drone',
      image:
        'https://images.unsplash.com/photo-1527977966376-1c8408f9f108?w=500&h=300&fit=crop',
      description:
        'Professional agricultural drone with crop monitoring and spraying capabilities',
      stock: 25,
      trend: 'up',
      quality: 98,
      harvest: 'All year',
      features: [
        '4K Camera',
        'GPS Navigation',
        'Pesticide Spraying',
        '2-hour Flight Time',
      ],
    },
    {
      name: 'Smart Irrigation System',
      price: '$3,200',
      organic: false,
      category: 'irrigation',
      image:
        'https://images.unsplash.com/photo-1586771107445-d3ca888129ff?w=500&h=300&fit=crop',
      description:
        'IoT-based automated irrigation system with moisture sensors and weather integration',
      stock: 50,
      trend: 'up',
      quality: 95,
      harvest: 'Demo Ready',
      features: [
        'Soil Moisture Sensors',
        'Weather API Integration',
        'Mobile App Control',
        'Water Usage Analytics',
      ],
    },
    {
      name: 'Precision Fertilizer Spreader',
      price: '$4,800',
      organic: false,
      category: 'equipment',
      image:
        'https://images.unsplash.com/photo-1625246333195-78d9c38ad449?w=500&h=300&fit=crop',
      description:
        'GPS-guided fertilizer spreader for precise nutrient application',
      stock: 35,
      trend: 'up',
      quality: 94,
      harvest: 'Ready to Ship',
      features: [
        'GPS Guidance',
        'Variable Rate Technology',
        'Soil Map Integration',
        'Fertilizer Efficiency',
      ],
    },
    {
      name: 'Greenhouse Climate Controller',
      price: '$2,100',
      organic: false,
      category: 'climate',
      image:
        'https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=500&h=300&fit=crop',
      description:
        'Automated climate control system for optimal greenhouse growing conditions',
      stock: 80,
      trend: 'stable',
      quality: 96,
      harvest: 'In Production',
      features: [
        'Temperature Control',
        'Humidity Regulation',
        'CO2 Monitoring',
        'Automated Ventilation',
      ],
    },

    {
      name: 'Soil Analysis Kit',
      price: '$680',
      organic: false,
      category: 'testing',
      image:
        'https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?w=500&h=300&fit=crop',
      description:
        'Comprehensive soil testing kit with digital analysis and mobile app integration',
      stock: 200,
      trend: 'stable',
      quality: 89,
      harvest: 'Best Seller',
      features: [
        'pH Testing',
        'Nutrient Analysis',
        'Organic Matter Detection',
        'Digital Reports',
      ],
    },
  ];

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high':
        return '#ef4444';
      case 'medium':
        return '#f59e0b';
      case 'low':
        return '#10b981';
      default:
        return '#6b7280';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp size={16} className='text-green-500' />;
      case 'down':
        return <TrendingUp size={16} className='text-red-500 rotate-180' />;
      default:
        return <Activity size={16} className='text-gray-500' />;
    }
  };

  return (
    <div style={{ fontFamily: 'system-ui, -apple-system, sans-serif' }}>
      <style jsx>{`
        .thai-farm-container {
          min-height: 100vh;
          background: linear-gradient(to bottom, #f0fdf4, #ffffff);
        }

        .header {
          position: sticky;
          top: 0;
          z-index: 50;
          width: 100%;
          border-bottom: 1px solid #e5e7eb;
          background: rgba(255, 255, 255, 0.95);
          backdrop-filter: blur(10px);
        }

        .header-content {
          max-width: 1200px;
          margin: 0 auto;
          display: flex;
          height: 64px;
          align-items: center;
          justify-content: space-between;
          padding: 0 20px;
        }

        .logo {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 20px;
          font-weight: bold;
          color: #16a34a;
          text-decoration: none;
        }

        .logo-icon {
          width: 32px;
          height: 32px;
          background: #16a34a;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 16px;
        }

        .nav {
          display: none;
          gap: 24px;
        }

        @media (min-width: 768px) {
          .nav {
            display: flex;
          }
        }

        .nav-link {
          font-size: 14px;
          font-weight: 500;
          color: #374151;
          text-decoration: none;
          transition: color 0.2s;
          cursor: pointer;
        }

        .nav-link:hover,
        .nav-link.active {
          color: #16a34a;
        }

        .login-btn {
          background: #16a34a;
          color: white;
          border: none;
          padding: 8px 16px;
          border-radius: 6px;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          transition: background-color 0.2s;
        }

        .login-btn:hover {
          background: #15803d;
        }

        .hero {
          position: relative;
          height: 500px;
          background-image: url('/images/hero-farm.jpg');
          background-size: cover;
          background-position: center;
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          color: white;
        }

        .hero::before {
          content: '';
          position: absolute;
          inset: 0;
          background: rgba(0, 0, 0, 0.4);
          z-index: 1;
        }

        .hero-content {
          position: relative;
          z-index: 2;
          max-width: 800px;
          padding: 0 20px;
        }

        .hero-title {
          font-size: 48px;
          font-weight: bold;
          line-height: 1.1;
          margin-bottom: 24px;
        }

        @media (max-width: 768px) {
          .hero-title {
            font-size: 32px;
          }
        }

        .hero-subtitle {
          font-size: 18px;
          margin-bottom: 32px;
          opacity: 0.9;
        }

        .hero-buttons {
          display: flex;
          gap: 16px;
          justify-content: center;
          flex-wrap: wrap;
        }

        .btn-primary {
          background: #16a34a;
          color: white;
          border: none;
          padding: 12px 24px;
          border-radius: 6px;
          font-size: 16px;
          font-weight: 500;
          cursor: pointer;
          transition: background-color 0.2s;
        }

        .btn-primary:hover {
          background: #15803d;
        }

        .btn-secondary {
          background: transparent;
          color: white;
          border: 2px solid white;
          padding: 10px 24px;
          border-radius: 6px;
          font-size: 16px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
        }

        .btn-secondary:hover {
          background: rgba(255, 255, 255, 0.1);
        }

        .real-time-banner {
          background: linear-gradient(135deg, #16a34a, #15803d);
          color: white;
          padding: 16px 20px;
          text-align: center;
          position: relative;
          overflow: hidden;
        }

        .real-time-banner::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(
            90deg,
            transparent,
            rgba(255, 255, 255, 0.1),
            transparent
          );
          animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
          0% {
            left: -100%;
          }
          100% {
            left: 100%;
          }
        }

        .real-time-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
          gap: 16px;
          max-width: 800px;
          margin: 0 auto;
        }

        .real-time-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 4px;
        }

        .real-time-value {
          font-size: 18px;
          font-weight: bold;
        }

        .real-time-label {
          font-size: 12px;
          opacity: 0.9;
        }

        .stats-section {
          padding: 64px 20px;
          background: white;
        }

        .stats-container {
          max-width: 1200px;
          margin: 0 auto;
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 32px;
        }

        .stat-item {
          text-align: center;
          background: #f9fafb;
          padding: 24px;
          border-radius: 12px;
          transition: transform 0.2s;
        }

        .stat-item:hover {
          transform: translateY(-4px);
        }

        .stat-icon {
          width: 64px;
          height: 64px;
          background: #dcfce7;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto 16px;
          overflow: hidden;
        }

        .stat-icon img {
          width: 40px;
          height: 40px;
          object-fit: cover;
          border-radius: 50%;
        }

        .stat-value {
          font-size: 32px;
          font-weight: bold;
          color: #16a34a;
          margin-bottom: 8px;
        }

        .stat-label {
          font-size: 14px;
          color: #6b7280;
          margin-bottom: 8px;
        }

        .stat-trend {
          font-size: 12px;
          color: #16a34a;
          font-weight: 500;
        }

        .challenges-section {
          padding: 80px 20px;
          background: #fef3c7;
        }

        .challenges-container {
          max-width: 1200px;
          margin: 0 auto;
        }

        .challenges-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 24px;
          margin-top: 32px;
        }

        .challenge-card {
          background: white;
          border-radius: 12px;
          padding: 20px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          border-left: 4px solid;
        }

        .challenge-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 12px;
        }

        .challenge-title {
          font-weight: 600;
          color: #111827;
        }

        .challenge-severity {
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
          color: white;
        }

        .challenge-description {
          color: #6b7280;
          margin-bottom: 12px;
          font-size: 14px;
        }

        .challenge-solution {
          background: #f0fdf4;
          padding: 12px;
          border-radius: 8px;
          margin-bottom: 12px;
        }

        .challenge-impact {
          font-size: 12px;
          color: #374151;
          font-weight: 500;
        }

        .products-section {
          padding: 80px 20px;
          background: #f0fdf4;
        }

        .section-container {
          max-width: 1200px;
          margin: 0 auto;
        }

        .section-header {
          text-align: center;
          margin-bottom: 48px;
        }

        .section-title {
          font-size: 36px;
          font-weight: bold;
          color: #111827;
          margin-bottom: 16px;
        }

        .section-subtitle {
          font-size: 18px;
          color: #6b7280;
          max-width: 600px;
          margin: 0 auto;
        }

        .products-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
          gap: 24px;
        }

        .product-card {
          background: white;
          border-radius: 12px;
          overflow: hidden;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          transition: all 0.2s;
        }

        .product-card:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          transform: translateY(-2px);
        }

        .product-image {
          width: 100%;
          height: 200px;
          position: relative;
          overflow: hidden;
        }

        .product-image img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.2s;
        }

        .product-card:hover .product-image img {
          transform: scale(1.05);
        }

        .product-badge {
          position: absolute;
          top: 8px;
          right: 8px;
          background: rgba(0, 0, 0, 0.8);
          color: white;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          display: flex;
          align-items: center;
          gap: 4px;
        }

        .product-content {
          padding: 16px;
        }

        .product-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 8px;
        }

        .product-name {
          font-weight: 600;
          color: #111827;
          margin-bottom: 4px;
        }

        .product-vendor {
          font-size: 14px;
          color: #6b7280;
        }

        .product-description {
          font-size: 12px;
          color: #9ca3af;
          margin-top: 4px;
        }

        .product-meta {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 12px;
          padding-top: 12px;
          border-top: 1px solid #f3f4f6;
        }

        .product-stock {
          font-size: 12px;
          color: #6b7280;
        }

        .product-quality {
          font-size: 12px;
          color: #16a34a;
          font-weight: 500;
        }

        .product-harvest {
          font-size: 12px;
          color: #f59e0b;
          font-weight: 500;
        }

        .organic-badge {
          background: #dcfce7;
          color: #166534;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 500;
        }

        .product-price {
          font-weight: 600;
          color: #16a34a;
          margin-top: 8px;
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .modal-overlay {
          position: fixed;
          inset: 0;
          z-index: 100;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(0, 0, 0, 0.5);
          backdrop-filter: blur(4px);
          padding: 16px;
        }

        .modal-content {
          background: white;
          border-radius: 12px;
          width: 100%;
          max-width: 400px;
          box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        .modal-header {
          padding: 24px 24px 0;
          text-align: center;
        }

        .modal-close {
          position: absolute;
          top: 16px;
          right: 16px;
          background: none;
          border: none;
          cursor: pointer;
          padding: 8px;
          border-radius: 4px;
          color: #6b7280;
        }

        .modal-close:hover {
          background: #f3f4f6;
        }

        .modal-title {
          font-size: 24px;
          font-weight: bold;
          color: #111827;
          margin-bottom: 8px;
        }

        .modal-subtitle {
          color: #6b7280;
          margin-bottom: 24px;
        }

        .form-container {
          padding: 0 24px 24px;
        }

        .form-group {
          margin-bottom: 16px;
        }

        .form-label {
          display: block;
          font-size: 14px;
          font-weight: 500;
          color: #374151;
          margin-bottom: 4px;
        }

        .form-input {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #d1d5db;
          border-radius: 6px;
          font-size: 14px;
          transition: border-color 0.2s;
        }

        .form-input:focus {
          outline: none;
          border-color: #16a34a;
          box-shadow: 0 0 0 3px rgba(22, 163, 74, 0.1);
        }

        .password-container {
          position: relative;
        }

        .password-toggle {
          position: absolute;
          right: 8px;
          top: 50%;
          transform: translateY(-50%);
          background: none;
          border: none;
          cursor: pointer;
          padding: 4px;
          color: #6b7280;
        }

        .form-footer {
          display: flex;
          flex-direction: column;
          gap: 12px;
        }

        .form-actions {
          display: flex;
          justify-content: space-between;
          gap: 12px;
        }

        .btn-cancel {
          background: none;
          border: 1px solid #d1d5db;
          color: #374151;
          padding: 8px 16px;
          border-radius: 6px;
          font-size: 14px;
          cursor: pointer;
          transition: all 0.2s;
        }

        .btn-cancel:hover {
          background: #f9fafb;
        }

        .btn-ghost {
          background: none;
          border: none;
          color: #6b7280;
          padding: 8px 16px;
          border-radius: 6px;
          font-size: 14px;
          cursor: pointer;
          transition: all 0.2s;
        }

        .btn-ghost:hover {
          background: #f9fafb;
        }

        .forgot-password {
          font-size: 12px;
          color: #16a34a;
          text-decoration: none;
        }

        .forgot-password:hover {
          text-decoration: underline;
        }

        .loading-spinner {
          display: inline-block;
          width: 16px;
          height: 16px;
          border: 2px solid transparent;
          border-top: 2px solid currentColor;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          margin-right: 8px;
        }

        @keyframes spin {
          to {
            transform: rotate(360deg);
          }
        }
      `}</style>

      <div className='thai-farm-container'>
        <header className='header'>
          <div className='header-content'>
            <a href='#' className='logo'>
              <div className='logo-icon'>🌾</div>
              Smart Agriculture
            </a>
            <nav className='nav'>
              <a
                href='#home'
                className={`nav-link ${activeTab === 'overview' ? 'active' : ''}`}
                onClick={() => setActiveTab('overview')}
              >
                Overview
              </a>
              <a
                href='#challenges'
                className={`nav-link ${activeTab === 'challenges' ? 'active' : ''}`}
                onClick={() => setActiveTab('challenges')}
              >
                Challenges
              </a>
              <a
                href='#products'
                className={`nav-link ${activeTab === 'products' ? 'active' : ''}`}
                onClick={() => setActiveTab('products')}
              >
                Products
              </a>
              <a
                href='#analytics'
                className={`nav-link ${activeTab === 'analytics' ? 'active' : ''}`}
                onClick={() => setActiveTab('analytics')}
              >
                Analytics
              </a>
            </nav>
            <button onClick={() => setShowLogin(true)} className='login-btn'>
              Login
            </button>
          </div>
        </header>

        <div className='real-time-banner'>
          <div className='real-time-grid'>
            <div className='real-time-item'>
              <Thermometer size={20} />
              <div className='real-time-value'>
                {realTimeData.temperature.toFixed(1)}°C
              </div>
              <div className='real-time-label'>Temperature</div>
            </div>
            <div className='real-time-item'>
              <Droplets size={20} />
              <div className='real-time-value'>
                {realTimeData.humidity.toFixed(0)}%
              </div>
              <div className='real-time-label'>Humidity</div>
            </div>
            <div className='real-time-item'>
              <Leaf size={20} />
              <div className='real-time-value'>
                {realTimeData.soilMoisture.toFixed(0)}%
              </div>
              <div className='real-time-label'>Soil Moisture</div>
            </div>
            <div className='real-time-item'>
              <Wind size={20} />
              <div className='real-time-value'>
                {realTimeData.windSpeed.toFixed(1)} km/h
              </div>
              <div className='real-time-label'>Wind Speed</div>
            </div>
            <div className='real-time-item'>
              <Sun size={20} />
              <div className='real-time-value'>
                {realTimeData.uvIndex.toFixed(1)}
              </div>
              <div className='real-time-label'>UV Index</div>
            </div>
          </div>
          <div style={{ marginTop: '8px', fontSize: '12px', opacity: 0.8 }}>
            {mounted && <>Last updated: {realTimeData.lastUpdated}</>}
          </div>
        </div>

        {activeTab === 'overview' && (
          <section id='home' className='hero'>
            <div className='hero-content'>
              <h1 className='hero-title'>
                Smart Thai Agriculture
                <br />
                AI-Powered Farming Solutions
              </h1>
              <p className='hero-subtitle'>
                Advanced agricultural technology platform combining IoT sensors,
                drone monitoring,
                <br />
                and AI analytics for optimal crop management and sustainable
                farming
              </p>
              <div className='hero-buttons'>
                <button
                  className='btn-primary'
                  onClick={() => setActiveTab('challenges')}
                >
                  View Challenges
                </button>
                <button
                  className='btn-secondary'
                  onClick={() => setActiveTab('analytics')}
                >
                  Analytics Dashboard
                </button>
              </div>
            </div>
          </section>
        )}

        {activeTab === 'overview' && (
          <section className='stats-section'>
            <div className='stats-container'>
              {stats.map((stat, index) => (
                <div key={index} className='stat-item'>
                  <div className='stat-icon'>
                    <img
                      src={stat.icon || '/placeholder.svg'}
                      alt={stat.label}
                    />
                  </div>
                  <div className='stat-value'>{stat.value}</div>
                  <div className='stat-label'>{stat.label}</div>
                  <div className='stat-trend'>{stat.trend} this month</div>
                </div>
              ))}
            </div>
          </section>
        )}

        {activeTab === 'challenges' && (
          <section className='challenges-section'>
            <div className='challenges-container'>
              <div className='section-header'>
                <h2 className='section-title'>
                  Agricultural Challenges & Solutions
                </h2>
                <p className='section-subtitle'>
                  AI-powered detection and resolution of farming challenges with
                  real-time monitoring
                </p>
              </div>
              <div className='challenges-grid'>
                {challenges.map(challenge => (
                  <div
                    key={challenge.id}
                    className='challenge-card'
                    style={{
                      borderLeftColor: getSeverityColor(challenge.severity),
                    }}
                  >
                    <div className='challenge-header'>
                      <AlertTriangle
                        size={20}
                        color={getSeverityColor(challenge.severity)}
                      />
                      <div className='challenge-title'>{challenge.title}</div>
                      <div
                        className='challenge-severity'
                        style={{
                          backgroundColor: getSeverityColor(challenge.severity),
                        }}
                      >
                        {challenge.severity.toUpperCase()}
                      </div>
                    </div>
                    <div className='challenge-description'>
                      {challenge.description}
                    </div>
                    <div className='challenge-solution'>
                      <strong>Recommended Solution:</strong>{' '}
                      {challenge.solution}
                    </div>
                    <div className='challenge-impact'>
                      <Shield
                        size={14}
                        style={{ display: 'inline', marginRight: '4px' }}
                      />
                      {challenge.impact}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>
        )}

        {(activeTab === 'products' || activeTab === 'overview') && (
          <section id='products' className='products-section'>
            <div className='section-container'>
              <div className='section-header'>
                <h2 className='section-title'>Smart Product Management</h2>
                <p className='section-subtitle'>
                  Real-time inventory tracking with quality monitoring and
                  market analytics
                </p>
              </div>
              <div className='products-grid'>
                {products.map((product, index) => (
                  <div key={index} className='product-card'>
                    <div className='product-image'>
                      <img
                        src={product.image || '/placeholder.svg'}
                        alt={product.name}
                      />
                      <div className='product-badge'>
                        <BarChart3 size={12} />
                        {product.quality}% Quality
                      </div>
                    </div>
                    <div className='product-content'>
                      <div className='product-header'>
                        <div>
                          <div className='product-name'>{product.name}</div>
                          <div className='product-vendor'>
                            Smart Farm Network
                          </div>
                          <div className='product-description'>
                            {product.description}
                          </div>
                        </div>
                        {product.organic && (
                          <div className='organic-badge'>Organic</div>
                        )}
                      </div>
                      <div className='product-price'>
                        {product.price}
                        {getTrendIcon(product.trend)}
                      </div>
                      <div className='product-meta'>
                        <div className='product-stock'>
                          Stock: {product.stock} kg
                        </div>
                        <div className='product-harvest'>{product.harvest}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>
        )}

        {activeTab === 'analytics' && (
          <section className='products-section'>
            <div className='section-container'>
              <div className='section-header'>
                <h2 className='section-title'>Advanced Analytics Dashboard</h2>
                <p className='section-subtitle'>
                  Comprehensive data analysis and predictive insights for
                  optimal farming decisions
                </p>
              </div>
              <div
                style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                  gap: '24px',
                  marginTop: '32px',
                }}
              >
                <div
                  style={{
                    background: 'white',
                    padding: '24px',
                    borderRadius: '12px',
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                  }}
                >
                  <h3
                    style={{
                      marginBottom: '16px',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                    }}
                  >
                    <TrendingUp size={20} color='#16a34a' />
                    Yield Prediction
                  </h3>
                  <div
                    style={{
                      fontSize: '32px',
                      fontWeight: 'bold',
                      color: '#16a34a',
                      marginBottom: '8px',
                    }}
                  >
                    +23.5%
                  </div>
                  <p style={{ color: '#6b7280', fontSize: '14px' }}>
                    Expected yield increase based on current conditions and AI
                    analysis
                  </p>
                </div>

                <div
                  style={{
                    background: 'white',
                    padding: '24px',
                    borderRadius: '12px',
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                  }}
                >
                  <h3
                    style={{
                      marginBottom: '16px',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                    }}
                  >
                    <Zap size={20} color='#f59e0b' />
                    Resource Efficiency
                  </h3>
                  <div
                    style={{
                      fontSize: '32px',
                      fontWeight: 'bold',
                      color: '#f59e0b',
                      marginBottom: '8px',
                    }}
                  >
                    87.2%
                  </div>
                  <p style={{ color: '#6b7280', fontSize: '14px' }}>
                    Water and fertilizer usage optimization score
                  </p>
                </div>

                <div
                  style={{
                    background: 'white',
                    padding: '24px',
                    borderRadius: '12px',
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
                  }}
                >
                  <h3
                    style={{
                      marginBottom: '16px',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                    }}
                  >
                    <Activity size={20} color='#ef4444' />
                    Risk Assessment
                  </h3>
                  <div
                    style={{
                      fontSize: '32px',
                      fontWeight: 'bold',
                      color: '#ef4444',
                      marginBottom: '8px',
                    }}
                  >
                    Low
                  </div>
                  <p style={{ color: '#6b7280', fontSize: '14px' }}>
                    Current risk level for crop diseases and pest infestations
                  </p>
                </div>
              </div>
            </div>
          </section>
        )}

        {showLogin && (
          <div className='modal-overlay'>
            <div className='modal-content' style={{ position: 'relative' }}>
              <button
                className='modal-close'
                onClick={() => {
                  setShowLogin(false);
                  setFormData({ email: '', password: '' });
                }}
              >
                <X size={20} />
              </button>

              <div className='modal-header'>
                <div style={{ fontSize: '32px', marginBottom: '16px' }}>🌾</div>
                <h2 className='modal-title'>Smart Agriculture Login</h2>
                <p className='modal-subtitle'>
                  Access your personalized agricultural dashboard
                </p>
              </div>

              <form onSubmit={handleSubmit} className='form-container'>
                <div className='form-group'>
                  <label htmlFor='email' className='form-label'>
                    Email
                  </label>
                  <input
                    id='email'
                    name='email'
                    type='email'
                    placeholder='<EMAIL>'
                    required
                    value={formData.email}
                    onChange={handleChange}
                    disabled={isLoading}
                    className='form-input'
                  />
                </div>

                <div className='form-group'>
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginBottom: '4px',
                    }}
                  >
                    <label htmlFor='password' className='form-label'>
                      Password
                    </label>
                    <a href='#' className='forgot-password'>
                      Forgot password?
                    </a>
                  </div>
                  <div className='password-container'>
                    <input
                      id='password'
                      name='password'
                      type={showPassword ? 'text' : 'password'}
                      placeholder='••••••••'
                      required
                      value={formData.password}
                      onChange={handleChange}
                      disabled={isLoading}
                      className='form-input'
                      style={{ paddingRight: '40px' }}
                    />
                    <button
                      type='button'
                      className='password-toggle'
                      onClick={() => setShowPassword(!showPassword)}
                      disabled={isLoading}
                    >
                      {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                    </button>
                  </div>
                </div>

                <div className='form-footer'>
                  <button
                    type='submit'
                    className='btn-primary'
                    disabled={isLoading}
                    style={{ width: '100%' }}
                  >
                    {isLoading ? (
                      <>
                        <span className='loading-spinner'></span>
                        Authenticating...
                      </>
                    ) : (
                      'Access Dashboard'
                    )}
                  </button>

                  <div className='form-actions'>
                    <button
                      type='button'
                      className='btn-cancel'
                      onClick={() => {
                        setShowLogin(false);
                        setFormData({ email: '', password: '' });
                      }}
                      disabled={isLoading}
                    >
                      Cancel
                    </button>
                    <button
                      type='button'
                      className='btn-ghost'
                      onClick={() =>
                        alert(
                          'Contact your agricultural advisor for registration'
                        )
                      }
                      disabled={isLoading}
                    >
                      Register Farm
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
