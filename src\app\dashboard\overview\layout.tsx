import PageContainer from '@/components/layout/page-container';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardAction,
  CardFooter,
} from '@/components/ui/card';
import {
  IconArrowRight,
  IconTrendingDown,
  IconTrendingUp,
} from '@tabler/icons-react';
import React from 'react';

export default function OverViewLayout({
  sales,
  pie_stats,
  bar_stats,
  area_stats,
}: {
  sales: React.ReactNode;
  pie_stats: React.ReactNode;
  bar_stats: React.ReactNode;
  area_stats: React.ReactNode;
}) {
  return (
    <PageContainer scrollable={false}>
      <div className='flex flex-1 flex-col space-y-2'>
        <div className='flex items-center justify-between space-y-2'>
          <h2 className='text-2xl font-bold tracking-tight'>
            Hi, Welcome Andy 👋
          </h2>
        </div>

        <div className='*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card grid grid-cols-1 gap-4 *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs md:grid-cols-2 lg:grid-cols-4'>
          <Card className='@container/card'>
            <CardHeader>
              <CardDescription>Total Revenue</CardDescription>
              <CardTitle className='text-primary flex items-center text-2xl font-semibold tabular-nums @[250px]/card:text-3xl'>
                ฿ 404,356.25
              </CardTitle>
              <CardAction>
                <Badge variant='outline' className='text-primary'>
                  <IconTrendingUp />
                  +12.5%
                </Badge>
              </CardAction>
            </CardHeader>
            <CardFooter className='flex-col items-start gap-1.5 text-sm'>
              <div className='text-muted-foreground line-clamp-1 flex gap-2 font-medium'>
                Comparison with last month <IconTrendingUp className='size-4' />
              </div>
            </CardFooter>
          </Card>
          <Card className='@container/card'>
            <CardHeader>
              <CardDescription>Harvest Volume Today</CardDescription>
              <CardTitle className='text-2xl text-primary font-semibold tabular-nums @[250px]/card:text-3xl'>
                980 kg
              </CardTitle>
              <CardAction>
                <Badge variant='outline' className='text-red-700'>
                  <IconTrendingDown />
                  -10%
                </Badge>
              </CardAction>
            </CardHeader>
            <CardFooter className='flex-col items-start gap-1.5 text-sm'>
              <div className='line-clamp-1 flex gap-2 font-medium text-muted-foreground'>
                Down 10% this period <IconTrendingDown className='size-4' />
              </div>
            </CardFooter>
          </Card>
          <Card className='@container/card'>
            <CardHeader>
              <CardDescription>Orders in Progress</CardDescription>
              <CardTitle className='text-2xl text-primary font-semibold tabular-nums @[250px]/card:text-3xl'>
                18 Orders
              </CardTitle>
            </CardHeader>
            <CardFooter className='flex-col items-start gap-1.5 text-sm'>
              <div className='text-muted-foreground w-full line-clamp-1 justify-between items-center flex gap-2 font-medium'>
                <span>7 Domestic | 11 Export</span>
                <a
                  href='#'
                  className='text-primary hover:underline display-flex items-center'
                >
                  <span>Details</span>
                  <span className='inline-flex ml-1'>
                    <IconArrowRight size={16} />
                  </span>
                </a>
              </div>
            </CardFooter>
          </Card>
          <Card className='@container/card'>
            <CardHeader>
              <CardDescription>Current Inventory</CardDescription>
              <CardTitle className='text-2xl text-primary font-semibold tabular-nums @[250px]/card:text-3xl'>
                1,500 kg
              </CardTitle>
            </CardHeader>
            <CardFooter className='flex-col items-start gap-1.5 text-sm'>
              <div className='text-muted-foreground line-clamp-1 flex gap-2 font-medium'>
                980 kg Monthong | 520 kg Chanee
              </div>
            </CardFooter>
          </Card>
        </div>
        <div className='grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-7'>
          <div className='col-span-4'>{bar_stats}</div>
          <div className='col-span-4 md:col-span-3'>{sales}</div>
          <div className='col-span-4'>{area_stats}</div>
          <div className='col-span-4 md:col-span-3'>{pie_stats}</div>
        </div>
      </div>
    </PageContainer>
  );
}

export const metadata = {
  title: 'AgriWise ERP Overview',
  description: 'AgriWise ERP dashboard overview',
};
