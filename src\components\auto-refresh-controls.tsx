'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  RefreshInterval,
  INTERVAL_LABELS,
  useAutoRefresh
} from '@/hooks/use-auto-refresh';
import { IconRefresh, IconClock, IconInfoCircle } from '@tabler/icons-react';
import { cn } from '@/lib/utils';
import { useState } from 'react';

interface AutoRefreshControlsProps {
  onRefresh: () => void | Promise<void>;
  className?: string;
}

function formatTimeRemaining(ms: number): string {
  if (ms <= 0) return '';
  
  const minutes = Math.floor(ms / (1000 * 60));
  const seconds = Math.floor((ms % (1000 * 60)) / 1000);
  
  if (minutes > 0) {
    return `${minutes}m ${seconds}s`;
  }
  return `${seconds}s`;
}

export function AutoRefreshControls({ 
  onRefresh, 
  className 
}: AutoRefreshControlsProps) {
  const {
    intervalTime,
    setIntervalTime,
    isActive,
    timeUntilNext,
    manualRefresh,
    isRefreshing,
  } = useAutoRefresh({
    onRefresh,
    defaultInterval: 'off',
    storageKey: 'invoice-auto-refresh-interval',
  });

  // const [interval, setInterval] = useState<RefreshInterval>("off");

  return (
    <TooltipProvider>
      <div className={cn('flex items-center gap-2', className)}>
        {/* Auto-refresh interval selector */}
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1">
            <span className="text-sm text-muted-foreground hidden sm:inline">
              Auto-refresh:
            </span>
            <Tooltip>
              <TooltipTrigger asChild>
                <IconInfoCircle className="h-3 w-3 text-muted-foreground cursor-help" />
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-xs max-w-xs">
                  Automatically refresh invoice data at the selected interval.
                  New invoices will be highlighted when they appear.
                </p>
              </TooltipContent>
            </Tooltip>
          </div>
          <Select value={intervalTime} onValueChange={setIntervalTime}>
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="Select interval" />
            </SelectTrigger>
            <SelectContent>
              {Object.entries(INTERVAL_LABELS).map(([value, label]) => (
                <SelectItem key={value} value={value}>
                  {label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

      {/* Status indicator and countdown */}
      {isActive && (
        <div className="flex items-center gap-2">
          <Badge
            variant="outline"
            className={cn(
              "text-xs flex items-center gap-1 transition-colors",
              isRefreshing ? "bg-blue-50 border-blue-200 text-blue-700 dark:bg-blue-950/20 dark:border-blue-800 dark:text-blue-300" :
              timeUntilNext > 0 ? "bg-green-50 border-green-200 text-green-700 dark:bg-green-950/20 dark:border-green-800 dark:text-green-300" :
              "bg-orange-50 border-orange-200 text-orange-700 dark:bg-orange-950/20 dark:border-orange-800 dark:text-orange-300"
            )}
          >
            <IconClock className={cn(
              "h-3 w-3",
              isRefreshing && "animate-spin"
            )} />
            {isRefreshing ? 'Refreshing...' :
             timeUntilNext > 0 ? `Next: ${formatTimeRemaining(timeUntilNext)}` :
             'Ready to refresh'}
          </Badge>
        </div>
      )}

      {/* Manual refresh button */}
      <Button
        variant="outline"
        size="sm"
        onClick={manualRefresh}
        disabled={isRefreshing}
        className="flex items-center gap-1"
      >
        <IconRefresh 
          className={cn(
            'h-4 w-4',
            isRefreshing && 'animate-spin'
          )} 
        />
        <span className="hidden sm:inline">Refresh</span>
      </Button>
      </div>
    </TooltipProvider>
  );
}
