import {
  Card,
  CardHeader,
  CardContent,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";

const orderData = [
  {
    id: "ORD-2001",
    customer: "Golden King Co.",
    destination: "China",
    status: "Processing",
    volumeKg: 2600,
    deliveryDate: "2025-06-29",
    variety: "Monthong",
  },
  {
    id: "ORD-2002",
    customer: "FruitLink Exports",
    destination: "Korea",
    status: "Delivered",
    volumeKg: 1900,
    deliveryDate: "2025-06-24",
    variety: "Ri6",
  },
  {
    id: "ORD-2003",
    customer: "Tropical Orchard Ltd.",
    destination: "Thailand",
    status: "Overdue",
    volumeKg: 1400,
    deliveryDate: "2025-06-21",
    variety: "Musang King",
  },
  {
    id: "ORD-2004",
    customer: "Green Valley Traders",
    destination: "Vietnam",
    status: "Processing",
    volumeKg: 1100,
    deliveryDate: "2025-06-30",
    variety: "Chanee",
  },
  {
    id: "ORD-2005",
    customer: "Royal Fresh Ltd.",
    destination: "Malaysia",
    status: "Delivered",
    volumeKg: 1500,
    deliveryDate: "2025-06-25",
    variety: "Kanyao",
  },
];

export function RecentSales() {
  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle>Order Status Overview</CardTitle>
        <CardDescription>
          Last 5 export orders by status and volume
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {orderData.map((order) => (
            <div key={order.id} className="flex items-center gap-4">
              <div className="flex flex-col items-start min-w-[90px]">
                <a
                  href={`/orders/${order.id}`}
                  className="text-sm font-semibold text-primary hover:underline"
                >
                  {order.id}
                </a>
              </div>
              <div className="flex-1 min-w-0">
                <div className="font-medium text-sm truncate">
                  {order.customer}
                </div>
                <div className="text-sm text-muted-foreground">
                  {order.variety} &middot; {order.destination}
                </div>
              </div>
              <div className="flex-1 min-w-[90px]">
                <div className="text-sm text-muted-foreground">Delivery</div>
                <div className="text-sm text-muted-foreground">
                  {order.deliveryDate}
                </div>
              </div>
              <div className="flex flex-col items-end">
                <span
                  className={
                    "text-sm font-semibold " +
                    (order.status === "Delivered"
                      ? "text-green-600"
                      : order.status === "Processing"
                        ? "text-blue-600"
                        : "text-red-600")
                  }
                >
                  {order.status}
                </span>
                <span className="text-sm text-muted-foreground">
                  {order.volumeKg.toLocaleString()} kg
                </span>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
