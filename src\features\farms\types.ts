export type MLocation = {
	lat: number;
	lng: number;
};

export type SensorItemData = {
	key: string;
	name: string;
	valueName?: string; // Optional, used for sensors like temperature, humidity, etc.
	location: MLocation;
	value: number;
	battery?: number;
	unit?: string;
};

export enum FarmStatus {
	GOOD = "good",
	WARNING = "warning",
	DANGER = "danger",
}

export type FarmData = {
	id: string;
	name: string;
	status: FarmStatus; // good, warning, danger
	variety: string;
	address: string;
	location: MLocation[];
	healthAlert?: {
		pest: string;
		health: string;
	};
	droneData: {
		totalTree: number;
		healthTree: number;
		canopyCoverage: number;
		healthStatus: string;
		leafDistortion: string;
	};
	sensors: SensorItemData[];
	lastWatered: string;
};
