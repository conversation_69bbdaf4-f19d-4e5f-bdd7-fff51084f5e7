interface DroneIconProps {
  className?: string;
  size?: number;
}

export function DroneIcon({ className = "", size = 16 }: DroneIconProps) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      {/* Main body */}
      <rect x="8" y="10" width="8" height="4" rx="1" fill="currentColor" />

      {/* Propellers */}
      <circle cx="6" cy="6" r="3" />
      <circle cx="18" cy="6" r="3" />
      <circle cx="6" cy="18" r="3" />
      <circle cx="18" cy="18" r="3" />

      {/* Arms connecting to propellers */}
      <line x1="8" y1="10" x2="6" y2="6" />
      <line x1="16" y1="10" x2="18" y2="6" />
      <line x1="8" y1="14" x2="6" y2="18" />
      <line x1="16" y1="14" x2="18" y2="18" />

      {/* Center dot (camera/sensor) */}
      <circle cx="12" cy="12" r="1" fill="currentColor" />
    </svg>
  );
}
