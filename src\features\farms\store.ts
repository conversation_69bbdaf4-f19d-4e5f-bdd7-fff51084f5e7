import { create } from "zustand";
import { FarmData, FarmStatus } from "./types";

type FarmStateStore = {
  selectedFarm: FarmData | null;
  farms: FarmData[];
  setSelectedFarm: (farm: FarmData | null) => void;
};

const thaiDurianFarms: FarmData[] = [
  {
    id: "TRT-DRN-004",
    name: "Surat Thani Durian Farm",
    status: FarmStatus.GOOD,
    variety: "Phuang Manee",
    address: "Tambon Khlong Sok, Phanom District, Surat Thani, Thailand",
    location: [
      { lat: 12.650854, lng: 102.149552 },
      { lat: 12.651547, lng: 102.149705 },
      { lat: 12.650762, lng: 102.151012 },
      { lat: 12.651215, lng: 102.151437 },
      { lat: 12.649937, lng: 102.151462 },
      { lat: 12.65044, lng: 102.150477 },
    ],
    droneData: {
      totalTree: 100,
      healthTree: 98,
      canopyCoverage: 90.0,
      healthStatus: "Healthy",
      leafDistortion: "None",
    },
    sensors: [
      {
        key: "SM004",
        name: "Soil Moisture D1",
        valueName: "Soil Moisture",
        location: { lat: 12.650557, lng: 102.151129 },
        value: 68,
        battery: 80,
        unit: "%",
      },
      {
        key: "TEMP004",
        name: "Temp Sensor D",
        valueName: "Temperature",
        location: { lat: 12.650136, lng: 102.151363 },
        value: 27,
        battery: 82,
        unit: "°C",
      },
      {
        key: "HUM004",
        name: "Farm Humidity D",
        valueName: "Humidity",
        location: { lat: 12.65043, lng: 102.150853 },
        value: 80,
        battery: 78,
        unit: "%",
      },

      {
        key: "LIGHT001",
        name: "Light Intensity Sensor",
        valueName: "Light Intensity",
        location: { lat: 12.650859, lng: 102.150051 },
        value: 1200,
        battery: 90,
        unit: "lux",
      },
    ],
    lastWatered: "2025-06-30T16:00:00+07:00",
  },
  {
    id: "TRT-DRN-001",
    name: "Suan Thong Durian Garden",
    status: FarmStatus.WARNING,
    variety: "Monthong",
    address:
      "Tambon Phlapphla, Mueang Chanthaburi District, Chanthaburi, Thailand",
    location: [
      { lat: 12.651366, lng: 102.147724 },
      { lat: 12.652028, lng: 102.148006 },
      { lat: 12.652621, lng: 102.14923 },
      { lat: 12.652201, lng: 102.150055 },
      { lat: 12.651561, lng: 102.14994 },
      { lat: 12.651727, lng: 102.149393 },
      { lat: 12.651065, lng: 102.149009 },
      { lat: 12.65084, lng: 102.148491 },
    ],
    healthAlert: {
      pest: "Phytophthora fungus",
      health: "Many trees heavily infected, requiring urgent intervention",
    },
    droneData: {
      totalTree: 250,
      healthTree: 240,
      canopyCoverage: 92.0,
      healthStatus: "Excellent",
      leafDistortion: "None",
    },
    sensors: [
      {
        key: "SM001",
        name: "Soil Moisture A1",
        valueName: "Soil Moisture",
        location: { lat: 12.651994, lng: 102.14975 },
        value: 70,
        battery: 95,
        unit: "%",
      },
      {
        key: "TEMP001",
        name: "Ambient Temp Sensor",
        valueName: "Temperature",
        location: { lat: 12.651363, lng: 102.148349 },
        value: 29,
        battery: 90,
        unit: "°C",
      },
      {
        key: "HUM001",
        name: "Relative Humidity",
        valueName: "Humidity",
        location: { lat: 12.651277, lng: 102.148572 },
        value: 75,
        battery: 88,
        unit: "%",
      },
      {
        key: "PH001",
        name: "Soil pH Sensor",
        valueName: "pH Level",
        location: { lat: 12.651439, lng: 102.148947 },
        value: 6.2,
        battery: 92,
      },
      {
        key: "RAIN001",
        name: "Rainfall Monitor",
        valueName: "Rainfall",
        location: { lat: 12.651123, lng: 102.148567 },
        value: 0.0,
        battery: 87,
        unit: "km/h",
      },
    ],
    lastWatered: "2025-06-30T09:00:00+07:00",
  },
  {
    id: "TRT-DRN-002",
    name: "Rayong Durian Farm",
    status: FarmStatus.WARNING,
    variety: "Kanyao",
    address: "Tambon Chak Phai, Ban Khai District, Rayong, Thailand",
    location: [
      { lat: 12.649286, lng: 102.149398 },
      { lat: 12.649826, lng: 102.148603 },
      { lat: 12.650395, lng: 102.147195 },
      { lat: 12.651111, lng: 102.147619 },
      { lat: 12.650691, lng: 102.1485 },
      { lat: 12.651061, lng: 102.149191 },
      { lat: 12.650366, lng: 102.149934 },
    ],
    healthAlert: {
      pest: "Mealybugs",
      health: "Some trees showing signs of weakness, requiring treatment",
    },
    droneData: {
      totalTree: 180,
      healthTree: 175,
      canopyCoverage: 80.0,
      healthStatus: "Mild stress",
      leafDistortion: "Slight yellowing",
    },
    sensors: [
      {
        key: "SM002",
        name: "Soil Moisture B1",
        valueName: "Soil Moisture",
        location: { lat: 12.650613, lng: 102.147652 },
        value: 55,
        battery: 70,
        unit: "%",
      },
      {
        key: "TEMP002",
        name: "Temp Sensor Zone B",
        valueName: "Temperature",
        location: { lat: 12.649921, lng: 102.149238 },
        value: 31,
        battery: 65,
        unit: "°C",
      },
      {
        key: "HUM002",
        name: "Farm Humidity B",
        valueName: "Humidity",
        location: { lat: 12.650661, lng: 102.148232 },
        value: 68,
        battery: 72,
        unit: "%",
      },
      {
        key: "PH002",
        name: "pH Monitor B",
        valueName: "pH Level",
        location: { lat: 12.650465, lng: 102.149508 },
        value: 5.5,
        battery: 75,
      },
    ],
    lastWatered: "2025-06-29T10:00:00+07:00",
  },
  {
    id: "TRT-DRN-003",
    name: "King Monthong Orchard",
    status: FarmStatus.GOOD,
    variety: "Chanee",
    address:
      "Tambon Ao Noi, Mueang Prachuap Khiri Khan District, Prachuap Khiri Khan, Thailand",
    location: [
      { lat: 12.65201, lng: 102.150055 },
      { lat: 12.651985, lng: 102.150452 },
      { lat: 12.652891, lng: 102.150796 },
      { lat: 12.652423, lng: 102.151368 },
      { lat: 12.651742, lng: 102.150988 },
      { lat: 12.651423, lng: 102.151416 },
      { lat: 12.650848, lng: 102.151015 },
      { lat: 12.651546, lng: 102.15002 },
    ],
    // healthAlert: {
    // pest: "Phytophthora fungus",
    // health: "Many trees heavily infected, requiring urgent intervention",
    // },
    droneData: {
      totalTree: 120,
      healthTree: 120,
      canopyCoverage: 60.0,
      healthStatus: "Severe disease",
      leafDistortion: "Extensive defoliation and branch dieback",
    },
    sensors: [
      {
        key: "SM003",
        name: "Soil Moisture C1",
        valueName: "Soil Moisture",
        location: { lat: 12.651689, lng: 102.15015 },
        value: 30,
        battery: 40,
        unit: "%",
      },
      {
        key: "TEMP003",
        name: "Temp Sensor Zone C",
        valueName: "Temperature",
        location: { lat: 12.651444, lng: 102.150539 },
        value: 32,
        battery: 35,
        unit: "°C",
      },
      {
        key: "HUM003",
        name: "Farm Humidity C",
        valueName: "Humidity",
        location: { lat: 12.65221, lng: 102.150805 },
        value: 60,
        battery: 45,
        unit: "%",
      },
      {
        key: "PH003",
        name: "pH Monitor C",
        valueName: "pH Level",
        location: { lat: 12.652617, lng: 102.150905 },
        value: 7.0,
        battery: 50,
      },
      {
        key: "WLVL001",
        name: "Water Level Sensor",
        valueName: "Water Level",
        location: { lat: 12.651231, lng: 102.151108 },
        value: 0.5,
        battery: 60,
        unit: "m",
      },
    ],
    lastWatered: "2025-06-28T09:00:00+07:00",
  },
  {
    id: "TRT-DRN-005",
    name: "Golden Thorn Plantation",
    status: FarmStatus.GOOD,
    variety: "Monthong",
    address:
      "Tambon Khlong Narai, Mueang Chanthaburi District, Chanthaburi, Thailand",
    location: [
      { lat: 12.649785, lng: 102.15149 },
      { lat: 12.650346, lng: 102.15003 },
      { lat: 12.649183, lng: 102.149508 },
      { lat: 12.648731, lng: 102.150146 },
      { lat: 12.648988, lng: 102.150367 },
      { lat: 12.648572, lng: 102.151476 },
    ],
    //healthAlert: {
    //pest: "Scale insects",
    //health: "Minor infestation, monitor closely",
    //},
    droneData: {
      totalTree: 80,
      healthTree: 79,
      canopyCoverage: 88.0,
      healthStatus: "Good with minor issues",
      leafDistortion: "Slight discoloration",
    },
    sensors: [
      {
        key: "SM005",
        name: "Soil Moisture E1",
        valueName: "Soil Moisture",
        location: { lat: 12.649704, lng: 102.149969 },
        value: 60,
        battery: 75,
        unit: "%",
      },
      {
        key: "TEMP005",
        name: "Temp Sensor E",
        valueName: "Temperature",
        location: { lat: 12.649156, lng: 102.150076 },
        value: 30,
        battery: 70,
        unit: "°C",
      },
      {
        key: "HUM005",
        name: "Farm Humidity E",
        valueName: "Humidity",
        location: { lat: 12.649486, lng: 102.150903 },
        value: 72,
        battery: 78,
        unit: "%",
      },
      {
        key: "PH005",
        name: "pH Monitor E",
        valueName: "pH Level",
        location: { lat: 12.648965, lng: 102.151141 },
        value: 6.3,
        battery: 80,
      },
    ],
    lastWatered: "2025-06-30T08:00:00+07:00",
  },
];

const useFarmStore = create<FarmStateStore>((set) => ({
  selectedFarm: thaiDurianFarms[0], // Initialize with the first farm or null if no farms
  farms: thaiDurianFarms, // Initialize with fake farms data
  setSelectedFarm: (farm) => set({ selectedFarm: farm }),
}));

export default useFarmStore;
