import { NextRequest, NextResponse } from 'next/server';

// ERPNext API configuration
const ERPNEXT_BASE_URL = process.env.NEXT_ERPNEXT_BASE_URL;
const API_TOKEN = process.env.NEXT_ERPNEXT_API_TOKEN;

// Define the ERPNext Purchase Invoice response type
interface ERPNextInvoiceDetail {
  name: string;
  doctype: string;
  naming_series: string;
  supplier: string;
  supplier_address: string;
  posting_date: string;
  due_date: string;
  bill_no: string;
  bill_date: string;
  currency: string;
  conversion_rate: number;
  company: string;
  cost_center: string;
  project?: string;
  taxes_and_charges: string;
  items: any[];
  taxes: any[];
  total_qty: number;
  base_net_total: number;
  net_total: number;
  base_total_taxes_and_charges: number;
  total_taxes_and_charges: number;
  base_grand_total: number;
  grand_total: number;
  outstanding_amount: number;
  status: string;
  remarks: string;
}

interface ERPNextDetailResponse {
  data: ERPNextInvoiceDetail;
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Build ERPNext API URL for single document
    const apiUrl = `${ERPNEXT_BASE_URL}/api/resource/Purchase%20Invoice/${encodeURIComponent(id)}`;

    // Make the API call to ERPNext
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Authorization': `token ${API_TOKEN}`,
        'Content-Type': 'application/json',
        'Cookie': 'full_name=Guest; sid=Guest; system_user=no; user_id=Guest; user_image='
      },
    });

    if (!response.ok) {
      if (response.status === 404) {
        return NextResponse.json(
          {
            success: false,
            message: `Invoice with id ${id} not found`,
          },
          { status: 404 }
        );
      }
      throw new Error(`ERPNext API error: ${response.status} ${response.statusText}`);
    }

    const data: ERPNextDetailResponse = await response.json();
    const invoice = data.data;
    
    // Transform the data to match our expected format
    const transformedInvoice = {
      id: invoice.name,
      doctype: invoice.doctype || 'Purchase Invoice',
      naming_series: invoice.naming_series || 'PI-',
      supplier: invoice.supplier,
      supplier_address: invoice.supplier_address || '',
      posting_date: invoice.posting_date,
      due_date: invoice.due_date,
      bill_no: invoice.bill_no,
      bill_date: invoice.bill_date,
      currency: invoice.currency || 'THB',
      conversion_rate: invoice.conversion_rate || 1,
      company: invoice.company || '',
      cost_center: invoice.cost_center || '',
      project: invoice.project || '',
      taxes_and_charges: invoice.taxes_and_charges || '',
      items: invoice.items || [],
      taxes: invoice.taxes || [],
      total_qty: invoice.total_qty || 0,
      base_net_total: invoice.base_net_total,
      net_total: invoice.net_total || invoice.base_net_total,
      base_total_taxes_and_charges: invoice.base_total_taxes_and_charges || 0,
      total_taxes_and_charges: invoice.total_taxes_and_charges || 0,
      base_grand_total: invoice.base_grand_total,
      grand_total: invoice.grand_total,
      outstanding_amount: invoice.outstanding_amount || 0,
      status: invoice.status,
      remarks: invoice.remarks || '',
    };

    // Return the response in the expected format
    return NextResponse.json({
      success: true,
      time: new Date().toISOString(),
      message: `Invoice with id ${id} found`,
      invoice: transformedInvoice,
    });

  } catch (error) {
    console.error('Error fetching invoice from ERPNext:', error);
    
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to fetch invoice from ERPNext',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
