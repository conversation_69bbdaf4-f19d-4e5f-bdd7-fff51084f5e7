import { NextRequest, NextResponse } from 'next/server';

export default function middleware(req: NextRequest) {
  // Simulate authenticated user by adding a fake userId cookie
  const response = NextResponse.next();
  response.cookies.set('userId', 'fake-user-123');
  return response;
}

export const config = {
  matcher: [
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    '/(api|trpc)(.*)'
  ]
};
