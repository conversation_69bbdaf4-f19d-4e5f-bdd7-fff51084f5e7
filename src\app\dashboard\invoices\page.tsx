import PageContainer from '@/components/layout/page-container';
import { Button } from '@/components/ui/button';
import { Heading } from '@/components/ui/heading';
import { Separator } from '@/components/ui/separator';
import { DataTableSkeleton } from '@/components/ui/table/data-table-skeleton';
import InvoiceListingPage from '@/features/invoices/components/invoice-listing';
import { searchParamsCache } from '@/lib/searchparams';
import { IconUpload, IconDownload } from '@tabler/icons-react';
import { SearchParams } from 'nuqs/server';
import { Suspense } from 'react';
import ErrorBoundaryWrapper from '@/components/error-boundary-wrapper';

export const metadata = {
  title: 'Dashboard: Products',
};

type pageProps = {
  searchParams: Promise<SearchParams>;
};

export default async function Page(props: pageProps) {
  const searchParams = await props.searchParams;
  // Allow nested RSCs to access the search params (in a type-safe way)
  searchParamsCache.parse(searchParams);

  // This key is used for invoke suspense if any of the search params changed (used for filters).
  // const key = serialize({ ...searchParams });

  return (
    <PageContainer scrollable={false}>
      <div className='flex flex-1 flex-col space-y-4'>
        <div className='flex items-start justify-between'>
          <Heading title='Invoices' description='Manage purchase invoices' />
          <div>
            <Button variant='outline' className='mr-2'>
              <IconUpload className='h-4 w-4' /> Import
            </Button>
            <Button variant='outline'>
              <IconDownload className='h-4 w-4' /> Export
            </Button>
          </div>
        </div>
        <Separator />
        <ErrorBoundaryWrapper>
          <Suspense
            fallback={
              <DataTableSkeleton columnCount={5} rowCount={8} filterCount={2} />
            }
          >
            <InvoiceListingPage />
          </Suspense>
        </ErrorBoundaryWrapper>
      </div>
    </PageContainer>
  );
}
