"use client";

import type React from "react";
import { useEffect, useState } from "react";
import { BubbleChat } from "flowise-embed-react";

interface FlowiseChatBotProps {
  chatflowid: string;
  apiHost: string;
  className?: string;
}

const FlowiseChatBot: React.FC<FlowiseChatBotProps> = ({
  chatflowid,
  apiHost,
  className = "",
}) => {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);

    const forceApplyGreenTheme = () => {
      const style = document.createElement("style");
      style.id = "flowise-green-theme-override";
      style.textContent = `
/* HEADER - Apply green theme */
.flowise-chat-theme [class*="header"],
.flowise-chat-theme [data-testid="header"],
.flowise-chat-theme .chatbot-header {
  background: linear-gradient(135deg, #65A30D 0%, #4D7C0F 100%) !important;
  color: #FFFFFF !important;
  border-bottom: 2px solid #84CC16 !important;
  padding: 16px !important;
}

/* ULTRA SPECIFIC FIGURE TARGETING - Override Tailwind classes */
.flowise-chat-theme div figure.flex.justify-center.items-center.rounded-full.text-white.relative.flex-shrink-0.w-10.h-10.text-xl,
.flowise-chat-theme div figure.flex.justify-center.items-center.rounded-full.text-white.relative.flex-shrink-0,
.flowise-chat-theme div figure[class*="flex"][class*="justify-center"][class*="items-center"][class*="rounded-full"],
.flowise-chat-theme figure.flex.justify-center.items-center.rounded-full,
.flowise-chat-theme figure {
  background: linear-gradient(135deg, #4D7C0F 0%, #365314 100%) !important;
  border: 3px solid #84CC16 !important;
  box-shadow: 0 4px 12px rgba(77, 124, 15, 0.3) !important;
  background-color: #4D7C0F !important;
}

/* GUEST CONTAINER FIGURES - User avatars */
.flowise-chat-theme div.guest-container figure,
.flowise-chat-theme [class*="guest-container"] figure,
.flowise-chat-theme div[class*="guest"] figure {
  background: linear-gradient(135deg, #65A30D 0%, #4D7C0F 100%) !important;
  border: 3px solid #A3E635 !important;
  box-shadow: 0 4px 12px rgba(101, 163, 13, 0.4) !important;
  background-color: #65A30D !important;
}

/* HOST CONTAINER FIGURES - Bot avatars */
.flowise-chat-theme div.host-container figure,
.flowise-chat-theme [class*="host-container"] figure,
.flowise-chat-theme div[class*="host"] figure {
  background: linear-gradient(135deg, #4D7C0F 0%, #365314 100%) !important;
  border: 3px solid #84CC16 !important;
  box-shadow: 0 4px 12px rgba(77, 124, 15, 0.3) !important;
  background-color: #4D7C0F !important;
}

/* FORCE AVATAR IMAGES TO SHOW */
.flowise-chat-theme figure img[alt*="avatar"],
.flowise-chat-theme figure img[alt*="Avatar"],
.flowise-chat-theme figure img[alt*="Bot"],
.flowise-chat-theme figure img[alt*="bot"],
.flowise-chat-theme figure img[src*="data:image"] {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  width: 100% !important;
  height: 100% !important;
  object-fit: contain !important;
  filter: brightness(0) invert(1) !important;
}

/* CHAT BUBBLE BUTTON */
.flowise-chat-theme button[class*="bubble"],
.flowise-chat-theme [data-testid="bubble-button"],
.flowise-chat-theme .chatbot-bubble {
  background: linear-gradient(135deg, #65A30D 0%, #4D7C0F 100%) !important;
  color: #FFFFFF !important;
  border: 3px solid #FFFFFF !important;
  box-shadow: 0 6px 20px rgba(101, 163, 13, 0.4) !important;
  transition: all 0.3s ease !important;
}

/* Message containers */
.flowise-chat-theme .guest-container,
.flowise-chat-theme .host-container,
.flowise-chat-theme [data-testid*="bubble"] {
  display: flex !important;
  align-items: flex-end !important;
  margin: 12px 0 !important;
  gap: 8px !important;
}

/* Bot messages styling */
.flowise-chat-theme .chatbot-host-bubble,
.flowise-chat-theme [data-testid="host-bubble"] {
  background: #F8FAFC !important;
  border: 1px solid #E2E8F0 !important;
  border-radius: 18px 18px 18px 4px !important;
  padding: 12px 16px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
  color: #1e293b !important;
}

/* User messages styling */
.flowise-chat-theme .chatbot-guest-bubble,
.flowise-chat-theme [data-testid="guest-bubble"] {
  background: linear-gradient(135deg, #65A30D 0%, #4D7C0F 100%) !important;
  color: #FFFFFF !important;
  border-radius: 18px 18px 4px 18px !important;
  padding: 12px 16px !important;
  box-shadow: 0 2px 8px rgba(101, 163, 13, 0.2) !important;
}

/* SEND BUTTON */
.flowise-chat-theme .chatbot-button,
.flowise-chat-theme button[type="submit"],
.flowise-chat-theme [class*="send-button"],
.flowise-chat-theme button[class*="send"] {
  background: linear-gradient(135deg, #65A30D 0%, #4D7C0F 100%) !important;
  color: #FFFFFF !important;
  border: 2px solid #FFFFFF !important;
  box-shadow: 0 3px 10px rgba(101, 163, 13, 0.3) !important;
  border-radius: 10px !important;
  padding: 10px !important;
  transition: all 0.2s ease !important;
}

/* SEND ICON */
.flowise-chat-theme .chatbot-button svg,
.flowise-chat-theme button[type="submit"] svg,
.flowise-chat-theme [class*="send-button"] svg,
.flowise-chat-theme button[class*="send"] svg,
.flowise-chat-theme svg.send-icon,
.flowise-chat-theme svg[class*="send"],
.flowise-chat-theme [data-testid*="send"] svg {
  stroke: #FFFFFF !important;
  fill: none !important;
  color: #FFFFFF !important;
  stroke-width: 2.5px !important;
  filter: drop-shadow(0 1px 2px rgba(0,0,0,0.3)) !important;
}

/* INPUT FIELD */
.flowise-chat-theme input,
.flowise-chat-theme textarea {
  border: 2px solid #E2E8F0 !important;
  border-radius: 12px !important;
  padding: 12px 16px !important;
  transition: all 0.2s ease !important;
}

.flowise-chat-theme input:focus,
.flowise-chat-theme textarea:focus {
  border-color: #65A30D !important;
  box-shadow: 0 0 0 3px rgba(101, 163, 13, 0.1) !important;
  outline: none !important;
}

/* FEEDBACK BUTTONS */
.flowise-chat-theme [class*="feedback"] button {
  color: #65A30D !important;
  border: 1px solid #65A30D !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
}

.flowise-chat-theme [class*="feedback"] button:hover {
  background: #65A30D !important;
  color: #FFFFFF !important;
}

/* Hide branding */
.flowise-chat-theme [class*="footer"],
.flowise-chat-theme [class*="powered"],
.flowise-chat-theme a[href*="flowiseai.com"] {
  display: none !important;
}
`;

      const existingStyle = document.getElementById(
        "flowise-green-theme-override"
      );
      if (existingStyle) {
        existingStyle.remove();
      }

      document.head.appendChild(style);
    };

    // Apply theme with multiple attempts to ensure it catches dynamic content
    setTimeout(forceApplyGreenTheme, 100);
    setTimeout(forceApplyGreenTheme, 500);
    setTimeout(forceApplyGreenTheme, 1000);
    setTimeout(forceApplyGreenTheme, 2000);
    setTimeout(forceApplyGreenTheme, 3000);

    return () => {
      const style = document.getElementById("flowise-green-theme-override");
      if (style) style.remove();
    };
  }, []);

  // Create enhanced Tabler icons as data URLs
  const createMessageIcon = () => {
    const svg = `
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <path d="M8 9h8"/>
        <path d="M8 13h6"/>
        <path d="M18 4a3 3 0 0 1 3 3v8a3 3 0 0 1 -3 3h-5l-5 3v-3h-2a3 3 0 0 1 -3 -3v-8a3 3 0 0 1 3 -3h12z"/>
      </svg>
    `;
    return `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svg)}`;
  };

  const createUserIcon = () => {
    const svg = `
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <path d="M8 7a4 4 0 1 0 8 0a4 4 0 0 0 -8 0"/>
        <path d="M6 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2"/>
      </svg>
    `;
    return `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svg)}`;
  };

  const createRobotIcon = () => {
    const svg = `
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <path d="M7 7h10a2 2 0 0 1 2 2v1l1 1v3l-1 1v3a2 2 0 0 1 -2 2h-10a2 2 0 0 1 -2 -2v-3l-1 -1v-3l1 -1v-1a2 2 0 0 1 2 -2z"/>
        <path d="M10 16h4"/>
        <circle cx="8.5" cy="11.5" r=".5" fill="currentColor"/>
        <circle cx="15.5" cy="11.5" r=".5" fill="currentColor"/>
        <path d="M9 7l-1 -4"/>
        <path d="M15 7l1 -4"/>
      </svg>
    `;
    return `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svg)}`;
  };

  if (!isClient) {
    return null;
  }

  return (
    <div className={`flowise-chat-theme theme-green ${className}`}>
      <BubbleChat
        chatflowid={chatflowid}
        apiHost={apiHost}
        theme={{
          button: {
            backgroundColor: "#4D7C0F",
            right: 20,
            bottom: 20,
            size: 52,
            iconColor: "#FFFFFF",
            customIconSrc: createMessageIcon(),
            dragAndDrop: false,
          },
          tooltip: {
            showTooltip: true,
            tooltipMessage: "Hi There 👋!",
            tooltipBackgroundColor: "#4D7C0F",
            tooltipTextColor: "#FFFFFF",
            tooltipFontSize: 14,
          },
          chatWindow: {
            showTitle: true,
            title: "AI Assistant",
            titleAvatarSrc: createRobotIcon(),
            welcomeMessage: "Hello! How can I help you today?",
            backgroundColor: "#ffffff",
            height: 600,
            width: 400,
            fontSize: 14,
            botMessage: {
              backgroundColor: "#f8fafc",
              textColor: "#1e293b",
              showAvatar: true,
              avatarSrc: createRobotIcon(),
            },
            userMessage: {
              backgroundColor: "#4D7C0F",
              textColor: "#FFFFFF",
              showAvatar: true,
              avatarSrc: createUserIcon(),
            },
            textInput: {
              placeholder: "Type your question",
              backgroundColor: "#ffffff",
              textColor: "#1e293b",
              sendButtonColor: "#4D7C0F",
              autoFocus: false,
              sendMessageSound: false,
              receiveMessageSound: false,
            },
            feedback: {
              color: "#4D7C0F",
            },
            footer: {
              textColor: "transparent",
              text: "",
              company: "",
              companyLink: "",
            },
          },
        }}
      />
    </div>
  );
};

export default FlowiseChatBot;
