'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';
import {
  DropletIcon,
  LeafIcon,
  MapPin,
  OctagonAlertIcon,
  TreeDeciduous,
  TreesIcon,
  Cpu,
  Eye,
  Activity,
  Thermometer,
  Droplets,
  Sun,
  TestTube,
  Calendar,
  TrendingUp,
  Shield,
  Zap,
  Wifi,
  Signal,
} from 'lucide-react';
import { useState } from 'react';

type FarmData = {
  name: string;
  variety: string;
  status: string;
  address: string;
  lastWatered: string;
  healthAlert?: {
    health: string;
    pest: string;
  };
  droneData: {
    totalTree: number;
    healthTree: number;
    canopyCoverage: number;
    healthStatus: string;
    leafDistortion: string;
  };
  sensors: Array<{
    key: string;
    valueName?: string;
    value: string | number;
    unit?: string;
    battery?: number;
  }>;
};

// Farm status colors (you'll need to import this from your actual file)
const FarmStatusColors = {
  healthy: {
    bg: 'bg-gradient-to-br from-emerald-50/80 via-green-50/60 to-teal-50/40',
    darkBg:
      'dark:from-emerald-950/40 dark:via-green-950/30 dark:to-teal-950/20',
    border: 'border-emerald-200/60 dark:border-emerald-700/50',
    badge:
      'bg-emerald-100/90 text-emerald-800 dark:bg-emerald-900/80 dark:text-emerald-200 shadow-sm',
    accent: 'text-emerald-600 dark:text-emerald-400',
    glow: 'shadow-emerald-500/20 dark:shadow-emerald-400/10',
  },
  warning: {
    bg: 'bg-gradient-to-br from-amber-50/80 via-yellow-50/60 to-orange-50/40',
    darkBg:
      'dark:from-amber-950/40 dark:via-yellow-950/30 dark:to-orange-950/20',
    border: 'border-amber-200/60 dark:border-amber-700/50',
    badge:
      'bg-amber-100/90 text-amber-800 dark:bg-amber-900/80 dark:text-amber-200 shadow-sm',
    accent: 'text-amber-600 dark:text-amber-400',
    glow: 'shadow-amber-500/20 dark:shadow-amber-400/10',
  },
  critical: {
    bg: 'bg-gradient-to-br from-red-50/80 via-rose-50/60 to-pink-50/40',
    darkBg: 'dark:from-red-950/40 dark:via-rose-950/30 dark:to-pink-950/20',
    border: 'border-red-200/60 dark:border-red-700/50',
    badge:
      'bg-red-100/90 text-red-800 dark:bg-red-900/80 dark:text-red-200 shadow-sm',
    accent: 'text-red-600 dark:text-red-400',
    glow: 'shadow-red-500/20 dark:shadow-red-400/10',
  },
};

type Props = {
  farm: FarmData;
  isOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
};

function formatDate(dateString: string) {
  const date = new Date(dateString);
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();
  return `${day}/${month}/${year}`;
}

function FarmModal({ farm, isOpen, onOpenChange }: Props) {
  const [open, setOpen] = useState(isOpen || false);
  const statusConfig =
    FarmStatusColors[farm.status as keyof typeof FarmStatusColors] ||
    FarmStatusColors.healthy;

  const handleOpenChange = (status: boolean) => {
    setOpen(status);
    onOpenChange?.(status);
  };

  const getSensorIcon = (sensorType: string) => {
    const type = sensorType.toLowerCase();
    if (type.includes('temperature') || type.includes('temp'))
      return Thermometer;
    if (type.includes('humidity') || type.includes('hum')) return Droplets;
    if (type.includes('moisture') || type.includes('soil')) return DropletIcon;
    if (type.includes('light')) return Sun;
    if (type.includes('ph')) return TestTube;
    return Cpu;
  };

  const getBatteryColor = (battery: number) => {
    if (battery >= 80) return 'text-emerald-600 dark:text-emerald-400';
    if (battery >= 50) return 'text-amber-600 dark:text-amber-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getBatteryBgColor = (battery: number) => {
    if (battery >= 80)
      return 'bg-emerald-50/80 dark:bg-emerald-950/40 border-emerald-200/50 dark:border-emerald-800/50';
    if (battery >= 50)
      return 'bg-amber-50/80 dark:bg-amber-950/40 border-amber-200/50 dark:border-amber-800/50';
    return 'bg-red-50/80 dark:bg-red-950/40 border-red-200/50 dark:border-red-800/50';
  };

  const healthPercentage = Math.round(
    (farm.droneData.healthTree / farm.droneData.totalTree) * 100
  );

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        <Button
          variant='outline'
          size='sm'
          className='w-full group relative overflow-hidden bg-gradient-to-r from-blue-50/50 to-indigo-50/50 hover:from-blue-100/80 hover:to-indigo-100/80 dark:from-blue-950/30 dark:to-indigo-950/30 dark:hover:from-blue-900/50 dark:hover:to-indigo-900/50 border-blue-200/60 hover:border-blue-300/80 dark:border-blue-800/50 dark:hover:border-blue-700/80 transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/20 dark:hover:shadow-blue-400/10'
        >
          <div className='absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent dark:via-white/5 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700' />
          <Eye className='h-4 w-4 mr-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors relative z-10' />
          <span className='relative z-10 font-medium'>View Details</span>
        </Button>
      </DialogTrigger>

      <DialogContent className='w-[98vw] sm:w-[95vw] md:w-[90vw] lg:w-[1400px] xl:w-[1600px] 2xl:w-[1800px] min-w-[900px] h-[90vh] min-h-[600px] max-h-[90vh] p-0 border-0 shadow-xl bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl overflow-hidden rounded-2xl [&>button]:absolute [&>button]:top-4 [&>button]:right-6 [&>button]:z-30 [&>button]:p-3 [&>button]:rounded-2xl [&>button]:bg-white/90 [&>button]:dark:bg-gray-800/90 [&>button]:hover:bg-white [&>button]:dark:hover:bg-gray-800 [&>button]:transition-all [&>button]:duration-300 [&>button]:shadow-xl [&>button]:hover:shadow-xl [&>button]:backdrop-blur-md [&>button]:border-0 [&>button]:outline-none [&>button]:focus:outline-none [&>button]:focus:ring-0 [&>button]:focus:border-0 [&>button]:focus-visible:ring-2 [&>button]:focus-visible:ring-gray-400/50 [&>button]:focus-visible:ring-offset-2'>
        <ScrollArea className='h-full w-full overflow-y-auto'>
          <div className='h-full min-h-full'>
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.4, ease: [0.16, 1, 0.3, 1] }}
              className='pb-8'
            >
              <div className='relative overflow-hidden'>
                <div
                  className={cn(
                    'relative p-4 lg:p-6',
                    statusConfig.bg,
                    statusConfig.darkBg,
                    'border-b-2',
                    statusConfig.border
                  )}
                >
                  <div className='absolute inset-0 bg-gradient-to-br from-white/40 via-white/20 to-transparent dark:from-white/10 dark:via-white/5 dark:to-transparent' />
                  <div className='absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-white/30 to-transparent dark:from-white/8 rounded-full -translate-y-48 translate-x-48 blur-3xl' />
                  <div className='absolute bottom-0 left-0 w-64 h-64 bg-gradient-to-tr from-white/20 to-transparent dark:from-white/5 rounded-full translate-y-32 -translate-x-32 blur-2xl' />

                  <DialogHeader className='relative space-y-8'>
                    <div className='flex flex-col xl:justify-between gap-4'>
                      <div className='flex-1 min-w-0 pr-12 xl:pr-6'>
                        <motion.div
                          initial={{ opacity: 0, y: 30 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.1, duration: 0.6 }}
                        >
                          <DialogTitle className='text-xl sm:text-2xl lg:text-3xl xl:text-4xl font-bold bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 dark:from-gray-100 dark:via-gray-200 dark:to-gray-100 bg-clip-text text-transparent mb-4 leading-tight'>
                            {farm.name}
                          </DialogTitle>
                          {/* <div className='flex flex-wrap items-center gap-2 mb-6'>
                            <Badge
                              variant='secondary'
                              className='bg-white/90 dark:bg-gray-800/90 text-gray-700 dark:text-gray-300 font-semibold px-4 py-2 text-sm backdrop-blur-sm shadow-lg border border-white/50 dark:border-gray-700/50 hover:shadow-xl transition-shadow duration-300'
                            >
                              <LeafIcon className='h-4 w-4 mr-2 text-green-600 dark:text-green-400' />
                              {farm.variety}
                            </Badge>
                            <Badge
                              className={cn(
                                statusConfig.badge,
                                'font-bold px-4 py-2 text-sm border',
                                statusConfig.border
                              )}
                            >
                              <Shield className='h-4 w-4 mr-2' />
                              {farm.droneData.healthStatus}
                            </Badge>
                          </div> */}
                          <div className='flex items-start gap-2'>
                            <div className='p-3 bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900/50 dark:to-indigo-900/50 rounded-xl shadow-sm'>
                              <MapPin className='h-6 w-6 text-blue-600 dark:text-blue-400' />
                            </div>
                            <div className='flex-1'>
                              <p className='text-sm font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-1'>
                                Farm Location
                              </p>
                              <p className='text-base text-gray-700 dark:text-gray-300 leading-relaxed font-medium'>
                                {farm.address}
                              </p>
                            </div>
                          </div>
                        </motion.div>
                      </div>

                      <motion.div
                        initial={{ opacity: 0, x: 30 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.2, duration: 0.6 }}
                        className='flex flex-row gap-2 flex-shrink-0'
                      >
                        <div className='bg-white/90 dark:bg-gray-800/90 backdrop-blur-md rounded-2xl p-4 text-center shadow-xl border border-white/60 dark:border-gray-700/60 hover:shadow-3xl transition-all duration-300 group min-w-[140px]'>
                          <div className='relative'>
                            <p className='text-4xl xl:text-5xl font-bold bg-gradient-to-br from-gray-900 to-gray-700 dark:from-gray-100 dark:to-gray-300 bg-clip-text text-transparent mb-2 group-hover:scale-110 transition-transform duration-300'>
                              {farm.droneData.totalTree}
                            </p>
                            <p className='text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider'>
                              Total Trees
                            </p>
                          </div>
                        </div>
                        <div className='bg-white/90 dark:bg-gray-800/90 backdrop-blur-md rounded-2xl p-4 text-center shadow-xl border border-white/60 dark:border-gray-700/60 hover:shadow-3xl transition-all duration-300 group min-w-[140px]'>
                          <div className='relative'>
                            <p className='text-4xl xl:text-5xl font-bold bg-gradient-to-br from-emerald-600 to-green-600 dark:from-emerald-400 dark:to-green-400 bg-clip-text text-transparent mb-2 group-hover:scale-110 transition-transform duration-300'>
                              {farm.droneData.healthTree}
                            </p>
                            <p className='text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider'>
                              Healthy ({healthPercentage}%)
                            </p>
                          </div>
                        </div>
                      </motion.div>
                    </div>

                    {/* <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3, duration: 0.6 }}
                      className='flex items-start gap-2 bg-white/70 dark:bg-gray-800/70 backdrop-blur-md rounded-2xl p-5 shadow-lg border border-white/60 dark:border-gray-700/60'
                    >
                      <div className='p-3 bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900/50 dark:to-indigo-900/50 rounded-xl shadow-sm'>
                        <MapPin className='h-6 w-6 text-blue-600 dark:text-blue-400' />
                      </div>
                      <div className='flex-1'>
                        <p className='text-sm font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-1'>
                          Farm Location
                        </p>
                        <p className='text-base text-gray-700 dark:text-gray-300 leading-relaxed font-medium'>
                          {farm.address}
                        </p>
                      </div>
                    </motion.div> */}
                  </DialogHeader>
                </div>
              </div>

              <AnimatePresence>
                {farm.healthAlert && (
                  <motion.div
                    initial={{ opacity: 0, height: 0, y: -30 }}
                    animate={{ opacity: 1, height: 'auto', y: 0 }}
                    exit={{ opacity: 0, height: 0, y: -30 }}
                    transition={{ duration: 0.4, ease: [0.16, 1, 0.3, 1] }}
                    className='mx-4 sm:mx-4 lg:mx-6 mt-4'
                  >
                    <div className='bg-gradient-to-r from-red-50/90 via-rose-50/80 to-pink-50/70 dark:from-red-950/50 dark:via-rose-950/40 dark:to-pink-950/30 border-2 border-red-200/80 dark:border-red-800/60 rounded-2xl p-4 shadow-xl backdrop-blur-sm'>
                      <div className='flex-1 min-w-0'>
                        <h4 className='font-bold text-red-800 dark:text-red-200 mb-4 text-2xl flex items-center gap-3'>
                          <Zap className='h-6 w-6' />
                          Critical Health Alert
                        </h4>
                        <div className='grid sm:grid-cols-2 gap-4'>
                          <div className='bg-white/80 dark:bg-gray-800/80 rounded-2xl p-5 shadow-lg border border-red-100 dark:border-red-900/50'>
                            <p className='text-sm font-bold text-red-700 dark:text-red-300 uppercase mb-1'>
                              Health Issue
                            </p>
                            <p className='text-sm text-red-800 dark:text-red-200'>
                              {farm.healthAlert.health}
                            </p>
                          </div>
                          <div className='bg-white/80 dark:bg-gray-800/80 rounded-2xl p-5 shadow-lg border border-red-100 dark:border-red-900/50'>
                            <p className='text-sm font-bold text-red-700 dark:text-red-300 uppercase mb-1'>
                              Pest Detected
                            </p>
                            <p className='text-sm text-red-800 dark:text-red-200'>
                              {farm.healthAlert.pest}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>

              <div className='p-2 sm:p-4 lg:p-6 space-y-6'>
                <div className='grid grid-cols-1 xl:grid-cols-2 gap-2 xl:gap-4 min-h-[400px]'>
                  <motion.div
                    initial={{ opacity: 0, y: 40 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4, duration: 0.6 }}
                    className='bg-gradient-to-br from-emerald-50/90 via-green-50/80 to-teal-50/70 dark:from-emerald-950/50 dark:via-green-950/40 dark:to-teal-950/30 rounded-2xl p-4 border-2 border-emerald-200/60 dark:border-emerald-800/50 shadow-xl hover:shadow-3xl transition-all duration-500 backdrop-blur-sm group flex flex-col min-h-[500px]'
                  >
                    <div className='flex items-center gap-4 mb-4'>
                      <div className='p-4 bg-gradient-to-br from-emerald-100 to-green-100 dark:from-emerald-900/60 dark:to-green-900/60 rounded-2xl shadow-lg group-hover:shadow-xl transition-shadow duration-300'>
                        <Activity className='h-8 w-8 text-emerald-600 dark:text-emerald-400' />
                      </div>
                      <div>
                        <h4 className='text-2xl font-bold text-gray-900 dark:text-gray-100 mb-1'>
                          Drone Analysis
                        </h4>
                        <p className='text-sm text-gray-600 dark:text-gray-400 font-medium flex items-center gap-2'>
                          <Signal className='h-4 w-4' />
                          Aerial monitoring insights
                        </p>
                      </div>
                    </div>

                    <div className='space-y-5 flex-1'>
                      {[
                        {
                          icon: TreesIcon,
                          label: 'Total Trees',
                          value: farm.droneData.totalTree,
                          color: 'text-emerald-600 dark:text-emerald-400',
                          bgColor: 'bg-emerald-50/80 dark:bg-emerald-950/60',
                          borderColor:
                            'border-emerald-200/50 dark:border-emerald-800/50',
                        },
                        {
                          icon: TreeDeciduous,
                          label: 'Healthy Trees',
                          value: farm.droneData.healthTree,
                          color: 'text-green-600 dark:text-green-400',
                          bgColor: 'bg-green-50/80 dark:bg-green-950/60',
                          borderColor:
                            'border-green-200/50 dark:border-green-800/50',
                        },
                        {
                          icon: LeafIcon,
                          label: 'Canopy Coverage',
                          value: `${farm.droneData.canopyCoverage}%`,
                          color: 'text-teal-600 dark:text-teal-400',
                          bgColor: 'bg-teal-50/80 dark:bg-teal-950/60',
                          borderColor:
                            'border-teal-200/50 dark:border-teal-800/50',
                        },
                      ].map((item, index) => (
                        <motion.div
                          key={item.label}
                          initial={{ opacity: 0, x: -30 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{
                            delay: 0.5 + index * 0.1,
                            duration: 0.5,
                          }}
                          className='flex items-center justify-between p-3 bg-white/90 dark:bg-gray-800/90 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-white/60 dark:border-gray-700/60 group/item'
                        >
                          <div className='flex items-center gap-3'>
                            <div
                              className={cn(
                                'p-3 rounded-xl border',
                                item.bgColor,
                                item.borderColor
                              )}
                            >
                              <item.icon
                                className={cn('h-6 w-6', item.color)}
                              />
                            </div>
                            <span className='text-base font-bold text-gray-700 dark:text-gray-300'>
                              {item.label}
                            </span>
                          </div>
                          <span className='text-2xl font-bold text-gray-900 dark:text-gray-100 group-hover/item:scale-110 transition-transform duration-300'>
                            {item.value}
                          </span>
                        </motion.div>
                      ))}

                      <Separator className='my-6 bg-gradient-to-r from-transparent via-gray-300 dark:via-gray-600 to-transparent' />

                      <div className='space-y-4'>
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.8, duration: 0.5 }}
                          className='flex items-center justify-between p-4 bg-white/90 dark:bg-gray-800/90 rounded-2xl shadow-lg border border-white/60 dark:border-gray-700/60'
                        >
                          <div className='flex items-center gap-2'>
                            <TrendingUp className='h-6 w-6 text-emerald-600 dark:text-emerald-400' />
                            <span className='text-base font-bold text-gray-700 dark:text-gray-300'>
                              Health Status
                            </span>
                          </div>
                          <Badge
                            className={cn(
                              statusConfig.badge,
                              'font-bold px-4 py-2 text-sm'
                            )}
                          >
                            {farm.droneData.healthStatus}
                          </Badge>
                        </motion.div>

                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.9, duration: 0.5 }}
                          className='flex items-center justify-between p-4 bg-white/90 dark:bg-gray-800/90 rounded-2xl shadow-lg border border-white/60 dark:border-gray-700/60'
                        >
                          <div className='flex items-center gap-2'>
                            <LeafIcon className='h-6 w-6 text-green-600 dark:text-green-400' />
                            <span className='text-base font-bold text-gray-700 dark:text-gray-300'>
                              Leaf Condition
                            </span>
                          </div>
                          <span className='text-base font-semibold text-gray-600 dark:text-gray-400 bg-gray-50/80 dark:bg-gray-700/80 px-4 py-2 rounded-xl'>
                            {farm.droneData.leafDistortion}
                          </span>
                        </motion.div>
                      </div>
                    </div>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, y: 40 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5, duration: 0.6 }}
                    className='bg-gradient-to-br from-blue-50/90 via-cyan-50/80 to-indigo-50/70 dark:from-blue-950/50 dark:via-cyan-950/40 dark:to-indigo-950/30 rounded-2xl p-4 border-2 border-blue-200/60 dark:border-blue-800/50 shadow-xl hover:shadow-3xl transition-all duration-500 backdrop-blur-sm group flex flex-col min-h-[500px]'
                  >
                    <div className='flex items-center gap-4 mb-4'>
                      <div className='p-4 bg-gradient-to-br from-blue-100 to-cyan-100 dark:from-blue-900/60 dark:to-cyan-900/60 rounded-2xl shadow-lg group-hover:shadow-xl transition-shadow duration-300'>
                        <Cpu className='h-8 w-8 text-blue-600 dark:text-blue-400' />
                      </div>
                      <div>
                        <h4 className='text-2xl font-bold text-gray-900 dark:text-gray-100 mb-1'>
                          Sensor Network
                        </h4>
                        <p className='text-sm text-gray-600 dark:text-gray-400 font-medium flex items-center gap-2'>
                          <Wifi className='h-4 w-4' />
                          Real-time environmental monitoring
                        </p>
                      </div>
                    </div>

                    <div className='space-y-4 flex-1'>
                      {farm.sensors.map((sensor, index) => {
                        const SensorIcon = getSensorIcon(
                          sensor.valueName ?? ''
                        );
                        const batteryLevel = sensor.battery ?? 0;

                        return (
                          <motion.div
                            key={sensor.key}
                            initial={{ opacity: 0, x: 30 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{
                              delay: 0.6 + index * 0.1,
                              duration: 0.5,
                            }}
                            className='flex items-center justify-between gap-2 p-3 bg-white/90 dark:bg-gray-800/90 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-white/60 dark:border-gray-700/60 group/sensor'
                          >
                            <div className='flex items-center gap-3'>
                              <div className='p-3 bg-blue-50/80 dark:bg-blue-950/60 rounded-xl border border-blue-200/50 dark:border-blue-800/50 flex-shrink-0'>
                                <SensorIcon className='h-6 w-6 text-blue-600 dark:text-blue-400' />
                              </div>
                              <span className='text-base font-bold text-gray-700 dark:text-gray-300'>
                                {sensor.valueName}
                              </span>
                            </div>

                            <div className='flex items-baseline gap-1.5'>
                              <span className='text-2xl font-bold text-gray-900 dark:text-gray-100 group-hover/sensor:scale-110 transition-transform duration-300'>
                                {sensor.value}
                              </span>
                              {sensor.unit && (
                                <span className='text-base text-gray-500 font-medium'>
                                  {sensor.unit}
                                </span>
                              )}
                            </div>
                          </motion.div>
                        );
                      })}

                      <Separator className='my-6 bg-gradient-to-r from-transparent via-gray-300 dark:via-gray-600 to-transparent' />

                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 1.0, duration: 0.5 }}
                        className='flex items-center justify-between p-4 bg-gradient-to-r from-blue-50/90 to-cyan-50/90 dark:from-blue-950/60 dark:to-cyan-950/60 rounded-2xl border-2 border-blue-200/60 dark:border-blue-800/50 shadow-lg'
                      >
                        <div className='flex items-center gap-3'>
                          <div className='p-3 bg-blue-100/80 dark:bg-blue-900/60 rounded-xl border border-blue-200/50 dark:border-blue-800/50'>
                            <DropletIcon className='h-6 w-6 text-blue-600 dark:text-blue-400' />
                          </div>
                          <div>
                            <span className='text-base font-bold text-gray-700 dark:text-gray-300 block'>
                              Last Irrigation
                            </span>
                          </div>
                        </div>
                        <span className='text-base font-bold text-gray-600 dark:text-gray-400 bg-white/80 dark:bg-gray-800/80 px-4 py-3 rounded-xl shadow-sm border border-white/60 dark:border-gray-700/60'>
                          {formatDate(farm.lastWatered)}
                        </span>
                      </motion.div>
                    </div>
                  </motion.div>
                </div>
              </div>
            </motion.div>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}

export default FarmModal;
