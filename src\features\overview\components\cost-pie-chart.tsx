"use client";

import * as React from "react";
import { IconTrendingUp } from "@tabler/icons-react";
import { Label, Pie, <PERSON><PERSON><PERSON>, ResponsiveContainer } from "recharts";

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";

const chartData = [
  { category: "labor", costs: 52689, fill: "var(--primary)" },
  { category: "electric", costs: 28392, fill: "var(--primary-light)" },
  { category: "packaging", costs: 14024, fill: "var(--primary-lighter)" },
  { category: "logistics", costs: 29473, fill: "var(--primary-dark)" },
  { category: "other", costs: 8790, fill: "var(--primary-darker)" },
];

const chartConfig = {
  costs: {
    label: "Costs",
  },
  labor: {
    label: "Labor",
    color: "var(--primary)",
  },
  electric: {
    label: "Electricity & Storage",
    color: "var(--primary)",
  },
  packaging: {
    label: "Packaging Materials",
    color: "var(--primary)",
  },
  logistics: {
    label: "Logistics",
    color: "var(--primary)",
  },
  other: {
    label: "Other",
    color: "var(--primary)",
  },
} satisfies ChartConfig;

export function CostPieChart() {
  const total = React.useMemo(() => {
    return chartData.reduce((acc, curr) => acc + curr.costs, 0);
  }, []);

  return (
    <Card className="@container/card">
      <CardHeader>
        <CardTitle>Operational Cost Structure (Last 3 Months)</CardTitle>
        <CardDescription>
          <span className="hidden @[540px]/card:block">
            Distribution of total operational costs from March to May
          </span>
          <span className="@[540px]/card:hidden">Costs distribution</span>
        </CardDescription>
      </CardHeader>
      <CardContent className="px-2 pt-4 sm:px-6 sm:pt-6">
        <ChartContainer
          config={chartConfig}
          className="mx-auto aspect-square h-[250px] w-[100%]"
        >
          <PieChart width={500} height={400}>
            <defs>
              {["labor", "electric", "packaging", "logistics", "other"].map(
                (category, index) => (
                  <linearGradient
                    key={category}
                    id={`fill${category}`}
                    x1="0"
                    y1="0"
                    x2="0"
                    y2="1"
                  >
                    <stop
                      offset="0%"
                      stopColor="var(--primary)"
                      stopOpacity={1 - index * 0.15}
                    />
                    <stop
                      offset="100%"
                      stopColor="var(--primary)"
                      stopOpacity={0.8 - index * 0.15}
                    />
                  </linearGradient>
                )
              )}
            </defs>
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent hideLabel />}
            />
            <Pie
              data={chartData.map((item) => ({
                ...item,
                fill: `url(#fill${item.category})`,
                name: chartConfig[item.category as keyof typeof chartConfig]
                  .label,
              }))}
              dataKey="costs"
              nameKey="name"
              innerRadius={60}
              strokeWidth={2}
              stroke="var(--background)"
              label={({ name, percent }) =>
                `${name} (${(percent * 100).toFixed(0)}%)`
              }
            >
              <Label
                content={({ viewBox }) => {
                  if (viewBox && "cx" in viewBox && "cy" in viewBox) {
                    return (
                      <text
                        x={viewBox.cx}
                        y={viewBox.cy}
                        textAnchor="middle"
                        dominantBaseline="middle"
                      >
                        <tspan
                          x={viewBox.cx}
                          y={viewBox.cy}
                          className="fill-foreground text-xl font-bold"
                        >
                          ฿ {total.toLocaleString()}
                        </tspan>
                        <tspan
                          x={viewBox.cx}
                          y={(viewBox.cy || 0) + 24}
                          className="fill-muted-foreground text-sm"
                        >
                          Total Costs
                        </tspan>
                      </text>
                    );
                  }
                }}
              />
            </Pie>
          </PieChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col gap-2 text-sm">
        <div className="flex items-center gap-2 leading-none font-medium">
          Chrome leads with {((chartData[0].costs / total) * 100).toFixed(1)}
          % <IconTrendingUp className="h-4 w-4" />
        </div>
        <div className="text-muted-foreground leading-none">
          Based on data from January - June 2024
        </div>
      </CardFooter>
    </Card>
  );
}
