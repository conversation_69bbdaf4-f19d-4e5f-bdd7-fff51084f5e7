'use client';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { zodResolver } from '@hookform/resolvers/zod';
import { useSearchParams, useRouter } from 'next/navigation';
import { useTransition, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import * as z from 'zod';
import { Mail, Eye, EyeOff, Loader2, ArrowRight } from 'lucide-react';

const formSchema = z.object({
  email: z.string().email({ message: 'Enter a valid email address' }),
  password: z
    .string()
    .min(6, { message: 'Password must be at least 6 characters' }),
});

type UserFormValue = z.infer<typeof formSchema>;

export default function UserAuthForm() {
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get('callbackUrl');
  const [loading, startTransition] = useTransition();
  const [showPassword, setShowPassword] = useState(false);
  const [focusedField, setFocusedField] = useState<string | null>(null);

  const defaultValues = {
    email: '',
    password: '',
  };

  const form = useForm<UserFormValue>({
    resolver: zodResolver(formSchema),
    defaultValues,
  });

  const router = useRouter();

  const onSubmit = async (data: UserFormValue) => {
    startTransition(() => {
      if (typeof window !== 'undefined') {
        localStorage.setItem('isAuthenticated', 'true');
      }
      toast.success('Welcome to AgriWise ERP! 🌱', {
        description: 'Successfully signed in to your agricultural dashboard',
      });
      router.push('/dashboard/overview');
    });
  };

  const handleGmailSignIn = () => {
    startTransition(() => {
      if (typeof window !== 'undefined') {
        localStorage.setItem('isAuthenticated', 'true');
      }
      toast.success('Welcome back! 🌱', {
        description: 'Successfully signed in with Gmail',
      });
      router.push('/dashboard/overview');
    });
  };

  return (
    <div className='w-full space-y-6'>
      <div className='bg-white rounded-3xl p-8 border border-gray-100 shadow-lg shadow-gray-100/50'>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className='w-full space-y-6'
          >
            <FormField
              control={form.control}
              name='email'
              render={({ field }) => (
                <FormItem>
                  <FormLabel className='text-gray-800 font-semibold text-base'>
                    Email Address
                  </FormLabel>
                  <FormControl>
                    <div className='relative group'>
                      <Mail className='absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 transition-colors duration-200 group-focus-within:text-lime-500 z-10' />
                      <Input
                        type='email'
                        placeholder='Enter your email address'
                        disabled={loading}
                        className={`pl-12 h-14 border-2 transition-all duration-200 bg-white text-base rounded-2xl ${
                          focusedField === 'email'
                            ? 'border-lime-400 ring-4 ring-lime-100 shadow-lg shadow-lime-200/50'
                            : 'border-gray-200 hover:border-lime-300'
                        }`}
                        onFocus={() => setFocusedField('email')}
                        {...field}
                        onBlur={() => {
                          setFocusedField(null);
                          field.onBlur();
                        }}
                      />
                    </div>
                  </FormControl>
                  <FormMessage className='text-red-500 text-sm' />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='password'
              render={({ field }) => (
                <FormItem>
                  <FormLabel className='text-gray-800 font-semibold text-base'>
                    Password
                  </FormLabel>
                  <FormControl>
                    <div className='relative group'>
                      <Input
                        type={showPassword ? 'text' : 'password'}
                        placeholder='Enter your password'
                        disabled={loading}
                        className={`pr-12 h-14 border-2 transition-all duration-200 bg-white text-base rounded-2xl ${
                          focusedField === 'password'
                            ? 'border-lime-400 ring-4 ring-lime-100 shadow-lg shadow-lime-200/50'
                            : 'border-gray-200 hover:border-lime-300'
                        }`}
                        onFocus={() => setFocusedField('password')}
                        {...field}
                        onBlur={() => {
                          setFocusedField(null);
                          field.onBlur();
                        }}
                      />
                      <button
                        type='button'
                        onClick={() => setShowPassword(!showPassword)}
                        className='absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-lime-600 transition-colors duration-200 z-10'
                      >
                        {showPassword ? (
                          <EyeOff className='h-5 w-5' />
                        ) : (
                          <Eye className='h-5 w-5' />
                        )}
                      </button>
                    </div>
                  </FormControl>
                  <FormMessage className='text-red-500 text-sm' />
                </FormItem>
              )}
            />

            <div className='flex items-center justify-between pt-2'>
              <div className='text-sm'>
                <a
                  href='/forgot-password'
                  className='text-primary hover:text-lime-700 font-semibold transition-colors duration-200'
                >
                  Forgot password?
                </a>
              </div>
            </div>

            <Button
              disabled={loading}
              className='w-full h-14 bg-gradient-to-r from-lime-500 to-green-600 hover:from-lime-600 hover:to-green-700 text-white font-semibold rounded-2xl shadow-lg shadow-lime-200/50 transition-all duration-200 hover:shadow-xl hover:shadow-lime-300/50 group text-base'
              type='submit'
            >
              {loading ? (
                <div className='flex items-center space-x-2'>
                  <Loader2 className='h-5 w-5 animate-spin' />
                  <span>Signing in...</span>
                </div>
              ) : (
                <div className='flex cursor-pointer items-center space-x-2'>
                  <span>Sign In with Email</span>
                  <ArrowRight className='h-5 w-5 transition-transform duration-200 group-hover:translate-x-1' />
                </div>
              )}
            </Button>
          </form>
        </Form>
      </div>

      <div className='relative'>
        <div className='absolute inset-0 flex items-center'>
          <span className='w-full border-t border-gray-200' />
        </div>
        <div className='relative flex justify-center text-sm uppercase'>
          <span className='bg-white px-4 text-gray-500 font-medium rounded-2xl'>
            Or continue with
          </span>
        </div>
      </div>

      <Button
        onClick={handleGmailSignIn}
        disabled={loading}
        variant='outline'
        className='w-full h-14 border-2 border-gray-200 hover:border-lime-300 hover:bg-lime-50 bg-white rounded-2xl transition-all duration-200 group shadow-sm hover:shadow-md text-base'
      >
        <div className='flex cursor-pointer items-center space-x-3 text-[#00C300]'>
          {/* Line Chat Logo SVG */}
          <svg
            xmlns='http://www.w3.org/2000/svg'
            x='0px'
            y='0px'
            width='100'
            height='100'
            viewBox='0 0 48 48'
            style={{ width: 50, height: 50 }}
          >
            <path
              fill='#00c300'
              d='M12.5,42h23c3.59,0,6.5-2.91,6.5-6.5v-23C42,8.91,39.09,6,35.5,6h-23C8.91,6,6,8.91,6,12.5v23C6,39.09,8.91,42,12.5,42z'
            ></path>
            <path
              fill='#fff'
              d='M37.113,22.417c0-5.865-5.88-10.637-13.107-10.637s-13.108,4.772-13.108,10.637c0,5.258,4.663,9.662,10.962,10.495c0.427,0.092,1.008,0.282,1.155,0.646c0.132,0.331,0.086,0.85,0.042,1.185c0,0-0.153,0.925-0.187,1.122c-0.057,0.331-0.263,1.296,1.135,0.707c1.399-0.589,7.548-4.445,10.298-7.611h-0.001C36.203,26.879,37.113,24.764,37.113,22.417z M18.875,25.907h-2.604c-0.379,0-0.687-0.308-0.687-0.688V20.01c0-0.379,0.308-0.687,0.687-0.687c0.379,0,0.687,0.308,0.687,0.687v4.521h1.917c0.379,0,0.687,0.308,0.687,0.687C19.562,25.598,19.254,25.907,18.875,25.907z M21.568,25.219c0,0.379-0.308,0.688-0.687,0.688s-0.687-0.308-0.687-0.688V20.01c0-0.379,0.308-0.687,0.687-0.687s0.687,0.308,0.687,0.687V25.219z M27.838,25.219c0,0.297-0.188,0.559-0.47,0.652c-0.071,0.024-0.145,0.036-0.218,0.036c-0.215,0-0.42-0.103-0.549-0.275l-2.669-3.635v3.222c0,0.379-0.308,0.688-0.688,0.688c-0.379,0-0.688-0.308-0.688-0.688V20.01c0-0.296,0.189-0.558,0.47-0.652c0.071-0.024,0.144-0.035,0.218-0.035c0.214,0,0.42,0.103,0.549,0.275l2.67,3.635V20.01c0-0.379,0.309-0.687,0.688-0.687c0.379,0,0.687,0.308,0.687,0.687V25.219z M32.052,21.927c0.379,0,0.688,0.308,0.688,0.688c0,0.379-0.308,0.687-0.688,0.687h-1.917v1.23h1.917c0.379,0,0.688,0.308,0.688,0.687c0,0.379-0.309,0.688-0.688,0.688h-2.604c-0.378,0-0.687-0.308-0.687-0.688v-2.603c0-0.001,0-0.001,0-0.001c0,0,0-0.001,0-0.001v-2.601c0-0.001,0-0.001,0-0.002c0-0.379,0.308-0.687,0.687-0.687h2.604c0.379,0,0.688,0.308,0.688,0.687s-0.308,0.687-0.688,0.687h-1.917v1.23H32.052z'
            ></path>
          </svg>
          <span className='font-semibold'>Continue with Line</span>
          <ArrowRight className='h-5 w-5 transition-transform duration-200 group-hover:translate-x-1' />
        </div>
      </Button>

      <div className='text-center'>
        <div className='inline-flex items-center space-x-2 px-3 py-2'>
          <span className='text-sm text-gray-600'>
            {"Don't have an account?"}
          </span>
          <a
            href='/sign-up'
            className='text-primary hover:text-lime-700 font-semibold text-sm transition-colors duration-200'
          >
            Sign up here
          </a>
        </div>
      </div>
    </div>
  );
}
