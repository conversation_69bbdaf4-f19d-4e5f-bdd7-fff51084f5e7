'use client';

import { ErrorBoundary } from 'react-error-boundary';
import ErrorBoundaryComponent from '@/components/error-boundary';
import { ReactNode } from 'react';

interface ErrorBoundaryWrapperProps {
  children: ReactNode;
}

export default function ErrorBoundaryWrapper({ children }: ErrorBoundaryWrapperProps) {
  const handleReset = () => {
    window.location.reload();
  };

  return (
    <ErrorBoundary
      FallbackComponent={ErrorBoundaryComponent}
      onReset={handleReset}
    >
      {children}
    </ErrorBoundary>
  );
}
