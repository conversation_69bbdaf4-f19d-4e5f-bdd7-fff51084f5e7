"use client";

import { FarmStatus } from "@/features/farms/types";
import { useMap } from "@vis.gl/react-google-maps";
import { Dispatch, SetStateAction, useEffect } from "react";

type Props = {
  id: string; // Unique key for the polygon
  location: google.maps.LatLngLiteral[];
  status?: FarmStatus; // Optional prop to handle focus state
  onClick?: Dispatch<SetStateAction<string | null>>;
};

const Polygon = ({ id, location, status, onClick }: Props) => {
  const map = useMap();

  useEffect(() => {
    if (!map) return;

    // Create polygon
    const polygon = new google.maps.Polygon({
      paths: location,
      strokeColor: status === FarmStatus.GOOD ? "#15803d" : "#ffba00",
      strokeOpacity: 1,
      strokeWeight: 1,
      fillColor: status === FarmStatus.GOOD ? "#86efac" : "#ffd230",
      fillOpacity: 0.3,
      clickable: true,
    });

    // Add polygon to map
    polygon.setMap(map);

    // Add click listener
    polygon.addListener("click", () => {
      if (onClick) {
        onClick(id);
        // polygon.setOptions({
        //   strokeColor: isFocused ? "#15803d" : "#f97316",
        //   fillColor: isFocused ? "#86efac" : "#fdba74",
        // });
      }
    });

    // Cleanup function
    return () => {
      polygon.setMap(null);
    };
  }, [map, location, id, onClick, status]);

  return null;
};

export default Polygon;
