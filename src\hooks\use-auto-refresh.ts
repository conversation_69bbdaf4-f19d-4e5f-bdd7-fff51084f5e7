'use client';

import { useCallback, useEffect, useRef, useState } from 'react';

export type RefreshInterval = 'off' | '5min' | '10min' | '30min';

interface UseAutoRefreshOptions {
  onRefresh: () => void | Promise<void>;
  defaultInterval?: RefreshInterval;
  storageKey?: string;
}

interface UseAutoRefreshReturn {
  intervalTime: RefreshInterval;
  setIntervalTime: (interval: RefreshInterval) => void;
  isActive: boolean;
  timeUntilNext: number;
  manualRefresh: () => void;
  isRefreshing: boolean;
}

const INTERVAL_MS: Record<RefreshInterval, number> = {
  'off': 0,
  '5min': 5 * 60 * 1000,
  '10min': 10 * 60 * 1000,
  '30min': 30 * 60 * 1000,
};

const INTERVAL_LABELS: Record<RefreshInterval, string> = {
  'off': 'Off',
  '5min': '5 minutes',
  '10min': '10 minutes',
  '30min': '30 minutes',
};

export function useAutoRefresh({
  onRefresh,
  defaultInterval = 'off',
  storageKey = 'invoice-auto-refresh-interval',
}: UseAutoRefreshOptions): UseAutoRefreshReturn {
  const [intervalTime, setIntervalState] = useState<RefreshInterval>(defaultInterval);
  const [timeUntilNext, setTimeUntilNext] = useState(0);
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  const intervalRef = useRef<number | undefined>(undefined);
  const countdownRef = useRef<number | undefined>(undefined);
  const startTimeRef = useRef<number>(0);

  // Load saved interval from localStorage on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem(storageKey) as RefreshInterval;
      if (saved && Object.keys(INTERVAL_MS).includes(saved)) {
        setIntervalState(saved);
      }
    }
  }, [storageKey]);

  // Save interval to localStorage when it changes
  const setIntervalTime = useCallback((newInterval: RefreshInterval) => {
    setIntervalState(newInterval);
    if (typeof window !== 'undefined') {
      localStorage.setItem(storageKey, newInterval);
    }
  }, [storageKey, setIntervalState]);

  // Manual refresh function
  const manualRefresh = useCallback(async () => {
    setIsRefreshing(true);
    try {
      await onRefresh();
    } finally {
      setIsRefreshing(false);
    }
  }, [onRefresh]);

  // Clear all timers
  const clearTimers = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = undefined;
    }
    if (countdownRef.current) {
      clearInterval(countdownRef.current);
      countdownRef.current = undefined;
    }
  }, []);

  // Start countdown timer
  const startCountdown = useCallback((duration: number) => {
    startTimeRef.current = Date.now();
    setTimeUntilNext(duration);

    countdownRef.current = window.setInterval(() => {
      const elapsed = Date.now() - startTimeRef.current;
      const remaining = Math.max(0, duration - elapsed);
      setTimeUntilNext(remaining);

      if (remaining <= 0) {
        if (countdownRef.current) {
          clearInterval(countdownRef.current);
          countdownRef.current = undefined;
        }
      }
    }, 1000);
  }, []);

  // Setup auto-refresh timer
  useEffect(() => {
    clearTimers();

    if (intervalTime === 'off') {
      setTimeUntilNext(0);
      return;
    }

    const intervalMs = INTERVAL_MS[intervalTime];
    
    const refreshAndScheduleNext = async () => {
      setIsRefreshing(true);
      try {
        await onRefresh();
      } finally {
        setIsRefreshing(false);
      }
      
      // Start countdown for next refresh
      startCountdown(intervalMs);
    };

    // Start countdown immediately
    startCountdown(intervalMs);

    // Set up the refresh interval
    intervalRef.current = window.setInterval(refreshAndScheduleNext, intervalMs);

    return clearTimers;
  }, [intervalTime, onRefresh, clearTimers, startCountdown]);

  // Cleanup on unmount
  useEffect(() => {
    return clearTimers;
  }, [clearTimers]);

  return {
    intervalTime,
    setIntervalTime,
    isActive: intervalTime !== 'off',
    timeUntilNext,
    manualRefresh,
    isRefreshing,
  };
}

export { INTERVAL_LABELS };
