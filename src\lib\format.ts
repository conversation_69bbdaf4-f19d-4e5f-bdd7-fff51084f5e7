export function formatDate(
	date: Date | string | number | undefined,
	opts: Intl.DateTimeFormatOptions = {}
) {
	if (!date) return "";

	try {
		return new Intl.DateTimeFormat("en-US", {
			month: opts.month ?? "long",
			day: opts.day ?? "numeric",
			year: opts.year ?? "numeric",
			...opts,
		}).format(new Date(date));
	} catch (_err) {
		return "";
	}
}

export function roundedPositiveNumber(num: number, digits: number = 0) {
	return Math.max(
		0,
		Math.round(num * Math.pow(10, digits)) / Math.pow(10, digits)
	);
}
