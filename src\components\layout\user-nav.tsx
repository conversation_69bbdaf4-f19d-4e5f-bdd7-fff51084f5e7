'use client';
import { But<PERSON> } from '@/components/ui/button';
import type React from 'react';
import { useState } from 'react';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { UserAvatarProfile } from '@/components/user-avatar-profile';
import { useRouter } from 'next/navigation';
import { Icons } from '@/components/icons';
import { v4 as uuidv4 } from 'uuid';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { IconBell } from '@tabler/icons-react';

const notificationRawData = [
  {
    type: 'pest_disease',
    level: 'high',
    title: 'Leaf-eating pests detected on durian trees',
    description:
      'AI camera detected abnormal bite marks on leaves, suspected pest infestation. Location: Plot 2, Garden A.',
    timestamp: '2025-07-03T13:00:00+07:00',
    action_required: true,
    suggested_action:
      'Apply organic pesticide and manually inspect the entire garden.',
  },
  {
    type: 'soil_issue',
    level: 'medium',
    title: 'Soil pH is below the recommended threshold',
    description:
      'Soil pH sensor at Plot 5, Garden B recorded pH = 4.9 (recommended range: 5.5–6.5).',
    timestamp: '2025-07-03T12:30:00+07:00',
    action_required: true,
    suggested_action:
      'Apply agricultural lime or pH balancing materials to reduce soil acidity.',
  },
  {
    type: 'humidity_issue',
    level: 'low',
    title: 'Soil moisture is below optimal level',
    description:
      'Moisture sensor at Plot 3, Garden C recorded 18% (optimal range: 25%–35%).',
    timestamp: '2025-07-03T12:00:00+07:00',
    action_required: false,
    suggested_action:
      'Monitor for another 6 hours. If not improved, consider additional irrigation.',
  },
];

const notificationTypeMap: Record<
  string,
  {
    label: string;
    color: string;
    icon: React.ReactNode;
  }
> = {
  pest_disease: {
    label: 'Pest/Disease',
    color: 'bg-red-500',
    icon: <Icons.warning className='w-4 h-4 text-white' />,
  },
  soil_issue: {
    label: 'Soil Issue',
    color: 'bg-yellow-600',
    icon: <Icons.tree className='w-4 h-4 text-white' />,
  },
  humidity_issue: {
    label: 'Humidity',
    color: 'bg-blue-500',
    icon: <Icons.weight className='w-4 h-4 text-white' />,
  },
};
const notificationLevelMap: Record<string, string> = {
  critical: 'bg-red-600 text-white',
  high: 'bg-red-500 text-white',
  medium: 'bg-amber-400 text-black',
  low: 'bg-blue-200 text-black',
};

type NotificationItem = {
  id: string;
  type: string;
  level: string;
  title: string;
  description: string;
  timestamp: string;
  action_required: boolean;
  suggested_action: string;
  read: boolean;
};

export function UserNav() {
  const user = {
    fullName: 'Andy Nguyen',
    emailAddresses: [{ emailAddress: '<EMAIL>' }],
  };
  const router = useRouter();
  const [notifications, setNotifications] = useState<NotificationItem[]>(
    notificationRawData.map((n, index) => ({
      ...n,
      id: index === 0 ? 'b2b7266e-53f1-4c25-933d-6f342c2f3b57' : uuidv4(),
      read: index > 1,
    }))
  );

  const unreadCount = notifications.filter(n => !n.read).length;

  const markAsRead = (id: string) => {
    setNotifications(prev =>
      prev.map(n => (n.id === id ? { ...n, read: true } : n))
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev => prev.map(n => ({ ...n, read: true })));
  };

  if (user) {
    const handleSignOut = () => {
      if (typeof window !== 'undefined') {
        localStorage.removeItem('isAuthenticated');
      }
      router.push('/auth/sign-in');
    };
    return (
      <div className='flex items-center gap-2'>
        <Popover>
          <PopoverTrigger asChild>
            <button
              className='relative h-8 w-8 rounded-full flex items-center justify-center hover:bg-accent transition-colors'
              aria-label='Show notifications'
            >
              <Icons.notification className='w-5 h-5' />
              {unreadCount > 0 && (
                <span className='absolute top-0 right-0 inline-flex items-center justify-center px-1.5 py-0.5 text-xs font-bold leading-none text-white bg-red-600 rounded-full'>
                  {unreadCount}
                </span>
              )}
            </button>
          </PopoverTrigger>
          <PopoverContent
            className='w-80 p-0 bg-popover rounded-xl shadow-lg border border-border'
            align='end'
            sideOffset={10}
          >
            <div className='flex flex-col'>
              <div className='flex items-center justify-between px-4 pt-3 pb-2'>
                <h3 className='text-xl text-foreground font-bold'>
                  Notifications
                </h3>
                {unreadCount > 0 && (
                  <Button
                    variant='link'
                    size='sm'
                    onClick={markAllAsRead}
                    className='h-auto p-0'
                  >
                    Mark all as read
                  </Button>
                )}
              </div>
              <div
                className='overflow-y-auto p-2 flex flex-col gap-y-1'
                style={{ maxHeight: 400 }}
              >
                {notifications.length === 0 ? (
                  <div className='text-center text-muted-foreground py-8'>
                    <div className='flex flex-col items-center gap-2'>
                      <IconBell className='w-10 h-10 text-muted-foreground' />{' '}
                      {/* Bell component used here */}
                      <span className='font-semibold'>
                        You're all caught up!
                      </span>
                    </div>
                  </div>
                ) : (
                  notifications.map(noti => {
                    const typeInfo = notificationTypeMap[noti.type] || {
                      label: noti.type,
                      color: 'bg-gray-400',
                      icon: null,
                    };
                    const levelClass =
                      notificationLevelMap[noti.level] ||
                      'bg-gray-200 text-black';
                    return (
                      <div
                        key={noti.id}
                        className={cn(
                          'relative p-3 rounded-lg flex gap-3 transition-colors hover:bg-muted/50',
                          !noti.read && 'bg-primary/10 dark:bg-primary/10'
                        )}
                        onClick={() => {
                          markAsRead(noti.id);
                          router.push(`/dashboard/farms?info_id=${noti.id}`);
                        }}
                      >
                        <div className='flex-grow cursor-pointer'>
                          <div className='flex items-center justify-between mb-1'>
                            <span
                              className={`flex items-center gap-1 px-2 py-0.5 rounded-full text-xs font-bold ${typeInfo.color} text-white`}
                            >
                              {typeInfo.icon}
                              {typeInfo.label}
                            </span>
                            <span
                              className={`px-2 py-0.5 rounded-full text-xs font-bold capitalize ${levelClass}`}
                            >
                              {noti.level}
                            </span>
                          </div>
                          <div className='font-bold text-sm text-foreground mt-2'>
                            {noti.title}
                          </div>
                          <div className='text-xs text-muted-foreground'>
                            {noti.description}
                          </div>
                          {noti.action_required && (
                            <div className='flex items-center gap-2 mt-2 px-3 py-2 bg-red-50 dark:bg-red-900/20 border-l-4 border-red-500 text-xs text-red-700 dark:text-red-400 rounded'>
                              <Icons.warning className='w-4 h-4 text-red-500' />
                              <span>
                                <span className='font-semibold'>
                                  Action Required:
                                </span>{' '}
                                {noti.suggested_action}
                              </span>
                            </div>
                          )}
                          <div className='flex items-center justify-between mt-2'>
                            <span className='text-xs text-muted-foreground'>
                              {format(
                                new Date(noti.timestamp),
                                'yyyy-MM-dd HH:mm'
                              )}
                            </span>
                            <Icons.arrowRight className='w-4 h-4 text-muted-foreground' />
                          </div>
                        </div>
                      </div>
                    );
                  })
                )}
              </div>
              <div className='p-2 border-t border-border'>
                <Button
                  variant='outline'
                  className='w-full bg-transparent'
                  onClick={() => router.push('/notifications')}
                >
                  View all notifications
                </Button>
              </div>
            </div>
          </PopoverContent>
        </Popover>
        {/* User Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='ghost' className='relative h-8 w-8 rounded-full'>
              <UserAvatarProfile user={user} />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className='w-56'
            align='end'
            sideOffset={10}
            forceMount
          >
            <DropdownMenuLabel className='font-normal'>
              <div className='flex flex-col space-y-1'>
                <p className='text-sm leading-none font-medium'>
                  {user.fullName}
                </p>
                <p className='text-muted-foreground text-xs leading-none'>
                  {user.emailAddresses[0].emailAddress}
                </p>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem
                onClick={() => router.push('/dashboard/profile')}
              >
                Profile
              </DropdownMenuItem>
              <DropdownMenuItem>Billing</DropdownMenuItem>
              <DropdownMenuItem>Settings</DropdownMenuItem>
              <DropdownMenuItem>New Team</DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleSignOut}>
              <Button>Sign out</Button>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    );
  }
}
