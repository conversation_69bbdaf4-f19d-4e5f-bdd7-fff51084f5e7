'use client';
import { Badge } from '@/components/ui/badge';
import { Invoice } from '@/constants/invoice/mock-api';
import { Column, ColumnDef } from '@tanstack/react-table';
import { INVOICE_STATUS } from '@/constants/invoice/status';
import { CellAction } from './cell-action';
import { DataTableColumnHeader } from '@/components/ui/table/data-table-column-header';
import { STATUS_OPTIONS, SUPPLIER_OPTIONS } from './options';
import Link from 'next/link';

export const columns: ColumnDef<Invoice>[] = [
  {
    accessorKey: 'id',
    header: ({ column }: { column: Column<Invoice, unknown> }) => (
      <DataTableColumnHeader column={column} title='ID' />
    ),
    cell: ({ cell }) => (
      <Link
        className='text-primary'
        href={`/dashboard/invoices/${cell.getValue<Invoice['id']>()}`}
      >
        {cell.getValue<Invoice['id']>()}
      </Link>
    ),
    enableSorting: true,
  },
  // {
  //   id: 'bill_no',
  //   accessorKey: 'bill_no',
  //   header: ({ column }: { column: Column<Invoice, unknown> }) => (
  //     <DataTableColumnHeader column={column} title='Bill No' />
  //   ),
  //   cell: ({ cell }) => (
  //     <Link href={`/dashboard/invoices/${cell.getValue<Invoice['id']>()}`}>
  //       {cell.getValue<Invoice['bill_no']>()}
  //     </Link>
  //   ),
  //   meta: {
  //     label: 'Bill No',
  //     placeholder: 'Search Invoices...',
  //     variant: 'text',
  //     icon: Text,
  //   },
  //   enableColumnFilter: true,
  // },
  {
    id: 'supplier',
    accessorKey: 'supplier',
    header: ({ column }: { column: Column<Invoice, unknown> }) => (
      <DataTableColumnHeader column={column} title='Supplier' />
    ),
    cell: ({ cell }) => {
      const value = cell.getValue<Invoice['supplier']>();

      return (
        <Link
          href={`/dashboard/supplier=${value}`}
          className='capitalize text-primary'
        >
          {value}
        </Link>
      );
    },
    enableColumnFilter: true,
    meta: {
      label: 'Supplier',
      variant: 'multiSelect',
      options: SUPPLIER_OPTIONS,
    },
  },
  {
    accessorKey: 'posting_date',
    header: ({ column }: { column: Column<Invoice, unknown> }) => (
      <DataTableColumnHeader column={column} title='Posting Date' />
    ),
    enableSorting: true,
  },
  {
    accessorKey: 'due_date',
    header: ({ column }: { column: Column<Invoice, unknown> }) => (
      <DataTableColumnHeader column={column} title='Due Date' />
    ),
    enableSorting: true,
  },
  // {
  //   accessorKey: 'bill_date',
  //   header: 'Bill Date',
  // },
  {
    accessorKey: 'base_net_total',
    header: ({ column }: { column: Column<Invoice, unknown> }) => (
      <DataTableColumnHeader column={column} title='Base Net Total' />
    ),
    cell: ({ cell }) => (
      <span>฿ {cell.getValue<number>().toLocaleString()}</span>
    ),
    enableSorting: true,
  },
  {
    accessorKey: 'grand_total',
    header: ({ column }: { column: Column<Invoice, unknown> }) => (
      <DataTableColumnHeader column={column} title='Grand Total' />
    ),
    cell: ({ cell }) => (
      <span>฿ {cell.getValue<number>().toLocaleString()}</span>
    ),
    enableSorting: true,
  },
  {
    accessorKey: 'status',
    header: ({ column }: { column: Column<Invoice, unknown> }) => (
      <DataTableColumnHeader column={column} title='Status' />
    ),
    cell: ({ cell }) => {
      const status = cell.getValue() as keyof typeof INVOICE_STATUS;
      return (
        <Badge
          variant='outline'
          className={`${INVOICE_STATUS[status].backgroundColor} ${INVOICE_STATUS[status].borderColor} ${INVOICE_STATUS[status].textColor}`}
        >
          {INVOICE_STATUS[status].label}
        </Badge>
      );
    },
    enableSorting: true,
    enableColumnFilter: true,
    meta: {
      label: 'Status',
      variant: 'multiSelect',
      options: STATUS_OPTIONS,
    },
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => <CellAction data={row.original} />,
  },
];
