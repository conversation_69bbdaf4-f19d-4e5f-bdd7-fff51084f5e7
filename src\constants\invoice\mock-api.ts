////////////////////////////////////////////////////////////////////////////////
// 🛑 Nothing in here has anything to do with Nextjs, it's just a fake database
////////////////////////////////////////////////////////////////////////////////

import { matchSorter } from 'match-sorter'; // For filtering
import data from './data.json'; // Import the invoice data

export const delay = (ms: number) =>
  new Promise(resolve => setTimeout(resolve, ms));

// Define the shape of Invoice Item data
export type InvoiceItem = {
  item_code: string;
  item_name: string;
  description: string;
  qty: number;
  uom: string;
  rate: number;
  amount: number;
  expense_account: string;
  warehouse: string;
};

// Define the shape of Invoice Tax data
export type InvoiceTax = {
  charge_type: string;
  account_head: string;
  rate: number;
  tax_amount: number;
  total: number;
};

// Define the shape of Invoice data
export type Invoice = {
  id: string;
  doctype: string;
  naming_series: string;
  supplier: string;
  supplier_address: string;
  posting_date: string;
  due_date: string;
  bill_no: string;
  bill_date: string;
  currency: string;
  conversion_rate: number;
  company: string;
  cost_center: string;
  project?: string;
  taxes_and_charges: string;
  items: InvoiceItem[];
  taxes: InvoiceTax[];
  total_qty: number;
  base_net_total: number;
  net_total: number;
  base_total_taxes_and_charges: number;
  total_taxes_and_charges: number;
  base_grand_total: number;
  grand_total: number;
  outstanding_amount: number;
  status: string;
  remarks: string;
};

// Mock invoice data store
export const fakeInvoices = {
  records: [] as Invoice[], // Holds the list of invoice objects

  // Initialize with sample data
  initialize() {
    // Load invoices from imported JSON data
    this.records = data.purchase_invoices as Invoice[];
  },

  // Get all invoices with optional filtering and search
  async getAll({
    suppliers = [],
    statuses = [],
    cost_centers = [],
    date_from,
    date_to,
    search,
  }: {
    suppliers?: string[];
    statuses?: string[];
    cost_centers?: string[];
    date_from?: string;
    date_to?: string;
    search?: string;
  }) {
    let invoices = [...this.records];

    // Filter invoices based on selected suppliers
    if (suppliers.length > 0) {
      invoices = invoices.filter(invoice =>
        suppliers.includes(invoice.supplier)
      );
    }

    // Filter invoices based on selected statuses
    if (statuses.length > 0) {
      invoices = invoices.filter(invoice => statuses.includes(invoice.status));
    }

    // Filter invoices based on selected cost centers
    if (cost_centers.length > 0) {
      invoices = invoices.filter(invoice =>
        cost_centers.includes(invoice.cost_center)
      );
    }

    // Filter invoices based on date range
    if (date_from) {
      invoices = invoices.filter(invoice => invoice.posting_date >= date_from);
    }

    if (date_to) {
      invoices = invoices.filter(invoice => invoice.posting_date <= date_to);
    }

    // Search functionality across multiple fields
    if (search) {
      invoices = matchSorter(invoices, search, {
        keys: [
          'supplier',
          'bill_no',
          'remarks',
          'posting_date',
          'due_date',
          'bill_date',
          'base_net_total',
          'grand_total',
          'status',
          'items.item_name',
          'items.description',
        ],
      });
    }

    return invoices;
  },

  // Get paginated results with optional filtering and search
  async getInvoices({
    page = 1,
    limit = 10,
    suppliers,
    statuses,
    cost_centers,
    date_from,
    date_to,
    search,
  }: {
    page?: number;
    limit?: number;
    suppliers?: string;
    statuses?: string;
    cost_centers?: string;
    date_from?: string;
    date_to?: string;
    search?: string;
  }) {
    await delay(500);

    const suppliersArray = suppliers ? suppliers.split('.') : [];
    const statusesArray = statuses ? statuses.split('.') : [];
    const costCentersArray = cost_centers ? cost_centers.split('.') : [];

    const allInvoices = await this.getAll({
      suppliers: suppliersArray,
      statuses: statusesArray,
      cost_centers: costCentersArray,
      date_from,
      date_to,
      search,
    });

    const totalInvoices = allInvoices.length;

    // Pagination logic
    const offset = (page - 1) * limit;
    const paginatedInvoices = allInvoices.slice(offset, offset + limit);

    // Mock current time
    const currentTime = new Date().toISOString();

    // Return paginated response
    return {
      success: true,
      time: currentTime,
      message: 'Invoice data for durian packing house operations',
      total_invoices: totalInvoices,
      offset,
      limit,
      invoices: paginatedInvoices,
    };
  },

  // Get a specific invoice by its bill number
  async getInvoiceByBillNo(bill_no: string) {
    await delay(500); // Simulate a delay

    // Find the invoice by its bill number
    const invoice = this.records.find(invoice => invoice.bill_no === bill_no);

    if (!invoice) {
      return {
        success: false,
        message: `Invoice with bill number ${bill_no} not found`,
      };
    }

    // Mock current time
    const currentTime = new Date().toISOString();

    return {
      success: true,
      time: currentTime,
      message: `Invoice with bill number ${bill_no} found`,
      invoice,
    };
  },

  // Get invoice by id
  async getInvoiceById(id: string) {
    await delay(500); // Simulate a delay

    // Find the invoice by its id
    const invoice = this.records.find(invoice => invoice.id === id);

    if (!invoice) {
      return {
        success: false,
        message: `Invoice with id ${id} not found`,
      };
    }

    // Mock current time
    const currentTime = new Date().toISOString();

    return {
      success: true,
      time: currentTime,
      message: `Invoice with id ${id} found`,
      invoice,
    };
  },

  // Get invoices by supplier
  async getInvoicesBySupplier(supplier: string) {
    await delay(500);

    const invoices = this.records.filter(invoice =>
      invoice.supplier.toLowerCase().includes(supplier.toLowerCase())
    );

    const currentTime = new Date().toISOString();

    return {
      success: true,
      time: currentTime,
      message: `Found ${invoices.length} invoices for supplier: ${supplier}`,
      total_invoices: invoices.length,
      invoices,
    };
  },

  // Get invoices by date range
  async getInvoicesByDateRange(date_from: string, date_to: string) {
    await delay(500);

    const invoices = this.records.filter(
      invoice =>
        invoice.posting_date >= date_from && invoice.posting_date <= date_to
    );

    const currentTime = new Date().toISOString();

    return {
      success: true,
      time: currentTime,
      message: `Found ${invoices.length} invoices between ${date_from} and ${date_to}`,
      total_invoices: invoices.length,
      invoices,
    };
  },

  // Get invoice summary statistics
  async getInvoiceSummary() {
    await delay(500);

    const totalInvoices = this.records.length;
    const totalAmount = this.records.reduce(
      (sum, invoice) => sum + invoice.grand_total,
      0
    );
    const totalOutstanding = this.records.reduce(
      (sum, invoice) => sum + invoice.outstanding_amount,
      0
    );

    const supplierStats = this.records.reduce(
      (acc, invoice) => {
        acc[invoice.supplier] =
          (acc[invoice.supplier] || 0) + invoice.grand_total;
        return acc;
      },
      {} as Record<string, number>
    );

    const statusStats = this.records.reduce(
      (acc, invoice) => {
        acc[invoice.status] = (acc[invoice.status] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>
    );

    const currentTime = new Date().toISOString();

    return {
      success: true,
      time: currentTime,
      message: 'Invoice summary statistics',
      summary: {
        total_invoices: totalInvoices,
        total_amount: totalAmount,
        total_outstanding: totalOutstanding,
        currency: 'THB',
        supplier_breakdown: supplierStats,
        status_breakdown: statusStats,
      },
    };
  },

  // Get all unique suppliers
  async getSuppliers() {
    await delay(300);

    const suppliers = Array.from(
      new Set(this.records.map(invoice => invoice.supplier))
    );
    const currentTime = new Date().toISOString();

    return {
      success: true,
      time: currentTime,
      message: 'List of all suppliers',
      suppliers: suppliers.sort(),
    };
  },

  // Get all unique cost centers
  async getCostCenters() {
    await delay(300);

    const costCenters = Array.from(
      new Set(this.records.map(invoice => invoice.cost_center))
    );
    const currentTime = new Date().toISOString();

    return {
      success: true,
      time: currentTime,
      message: 'List of all cost centers',
      cost_centers: costCenters.sort(),
    };
  },

  // Get items across all invoices with optional filtering
  async getItems({
    search,
    item_codes = [],
  }: {
    search?: string;
    item_codes?: string[];
  }) {
    await delay(800);

    let allItems: (InvoiceItem & {
      invoice_bill_no: string;
      posting_date: string;
    })[] = [];

    // Flatten all items from all invoices
    this.records.forEach(invoice => {
      invoice.items.forEach(item => {
        allItems.push({
          ...item,
          invoice_bill_no: invoice.bill_no,
          posting_date: invoice.posting_date,
        });
      });
    });

    // Filter by item codes if provided
    if (item_codes.length > 0) {
      allItems = allItems.filter(item => item_codes.includes(item.item_code));
    }

    // Search functionality
    if (search) {
      allItems = matchSorter(allItems, search, {
        keys: ['item_name', 'description', 'item_code'],
      });
    }

    const currentTime = new Date().toISOString();

    return {
      success: true,
      time: currentTime,
      message: 'Items from all invoices',
      total_items: allItems.length,
      items: allItems,
    };
  },
};

// Initialize sample invoices
fakeInvoices.initialize();
