"use client";
import { IconTrendingUp } from "@tabler/icons-react";
import { Area, AreaChart, CartesianGrid, XAxis, YAxis } from "recharts"; // Add YAxis import

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";

const chartData = [
  { month: "January", profit: 12000, cost: 6500 },
  { month: "February", profit: 15000, cost: 10200 },
  { month: "March", profit: 13000, cost: 8200 },
  { month: "April", profit: 16000, cost: 6100 },
  { month: "May", profit: 18000, cost: 10400 },
  { month: "June", profit: 20000, cost: 12200 },
];

const chartConfig = {
  visitors: {
    label: "Financials",
  },
  profit: {
    label: "Profit",
    color: "var(--color-lime-600)", // Changed to CSS variable
  },
  cost: {
    label: "Cost",
    color: "var(--color-ogrange-600)", // Changed to CSS variable
  },
} satisfies ChartConfig;

export function AreaGraph() {
  return (
    <Card className="@container/card">
      <CardHeader>
        <CardTitle>Profit and Cost Overview</CardTitle>
        <CardDescription>
          Financial performance for the last 6 months
        </CardDescription>
      </CardHeader>
      <CardContent className="px-2 pt-4 sm:px-6 sm:pt-6">
        <ChartContainer
          config={chartConfig}
          className="aspect-auto h-[250px] w-full"
        >
          <AreaChart
            data={chartData}
            margin={{
              left: 12,
              right: 12,
            }}
          >
            <defs>
              <linearGradient id="fillProfit" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor="var(--color-lime-600)"
                  stopOpacity={0.8}
                />
                <stop
                  offset="95%"
                  stopColor="var(--color-lime-600)"
                  stopOpacity={0.1}
                />
              </linearGradient>
              <linearGradient id="fillCost" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor="var(--color-orange-600)"
                  stopOpacity={0.8}
                />
                <stop
                  offset="95%"
                  stopColor="var(--color-orange-600)"
                  stopOpacity={0.1}
                />
              </linearGradient>
            </defs>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="month"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              minTickGap={32}
              tickFormatter={(value) => value.slice(0, 3)}
            />
            <YAxis
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              width={60}
            />
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent indicator="dot" />}
            />
            <Area
              dataKey="cost"
              type="natural"
              fill="url(#fillCost)"
              stroke="var(--color-orange-600)"
              stackId="a"
              yAxisId={0}
            />
            <Area
              dataKey="profit"
              type="natural"
              fill="url(#fillProfit)"
              stroke="var(--color-lime-600)"
              stackId="a"
              yAxisId={0}
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
      <CardFooter>
        <div className="flex w-full items-start gap-2 text-sm">
          <div className="grid gap-2">
            <div className="flex items-center gap-2 leading-none font-medium">
              Profit up by 11.1% in June
              <IconTrendingUp className="h-4 w-4 text-green-600" />
            </div>
            <div className="text-muted-foreground flex items-center gap-2 leading-none">
              January - June 2025
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}
