"use client";

import { ScrollArea } from "@/components/ui/scroll-area";
import { motion, AnimatePresence } from "framer-motion";
import { Sprout } from "lucide-react";
import EnhancedFarmCard from "./farm-card";
import useFarmStore from "./store";

function FarmList() {
  const farmStore = useFarmStore();

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.3 }}
      className="absolute top-4 right-4 h-fit w-[22rem] lg:w-[26rem] xl:w-[30rem] z-50 bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl rounded-2xl border border-gray-200/50 dark:border-gray-700/50 shadow-2xl shadow-black/10"
    >
      <div className="relative overflow-hidden rounded-t-2xl bg-gradient-to-r from-green-500 to-emerald-600 p-6">
        <div className="absolute inset-0 bg-gradient-to-r from-green-600/20 to-emerald-700/20" />
        <div className="relative flex items-center gap-3">
          <div className="p-2 bg-white/20 rounded-xl backdrop-blur-sm">
            <Sprout className="h-6 w-6 text-white" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-white">My Farms</h3>
            <p className="text-green-100 text-sm">
              {farmStore.farms.length} farms monitored
            </p>
          </div>
        </div>
      </div>

      <ScrollArea className="h-[calc(100vh-12rem)] w-full">
        <div className="p-4 space-y-3">
          <AnimatePresence>
            {farmStore.farms.map((farm, index) => (
              <motion.div
                key={farm.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{
                  duration: 0.3,
                  delay: index * 0.1,
                  ease: "easeOut",
                }}
              >
                <EnhancedFarmCard
                  farm={farm}
                  onSelect={farmStore.setSelectedFarm}
                />
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
      </ScrollArea>
    </motion.div>
  );
}

export default FarmList;
