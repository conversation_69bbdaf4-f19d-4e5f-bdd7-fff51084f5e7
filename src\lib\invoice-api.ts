// Client-side API service for invoices
import { Invoice } from '@/constants/invoice/mock-api';

interface InvoiceResponse {
  success: boolean;
  time: string;
  message: string;
  total_invoices: number;
  offset: number;
  limit: number;
  invoices: Invoice[];
}

interface InvoiceDetailResponse {
  success: boolean;
  time?: string;
  message: string;
  invoice?: Invoice;
}

interface GetInvoicesParams {
  page?: number;
  limit?: number;
  suppliers?: string;
  statuses?: string;
  cost_centers?: string;
  date_from?: string;
  date_to?: string;
  search?: string;
  order_by?: string;
}

export const invoiceApi = {
  // Get paginated invoices with filtering
  async getInvoices(params: GetInvoicesParams = {}): Promise<InvoiceResponse> {
    const searchParams = new URLSearchParams();
    
    // Add parameters to search params
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, value.toString());
      }
    });

    const response = await fetch(`/api/invoices?${searchParams.toString()}`);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch invoices: ${response.statusText}`);
    }

    return response.json();
  },

  // Get a specific invoice by ID
  async getInvoiceById(id: string): Promise<InvoiceDetailResponse> {
    const response = await fetch(`/api/invoices/${id}`);
    
    if (!response.ok) {
      if (response.status === 404) {
        return {
          success: false,
          message: `Invoice with id ${id} not found`,
        };
      }
      throw new Error(`Failed to fetch invoice: ${response.statusText}`);
    }

    return response.json();
  },

  // Get a specific invoice by bill number
  async getInvoiceByBillNo(billNo: string): Promise<InvoiceDetailResponse> {
    const response = await fetch(`/api/invoices/bill/${billNo}`);
    
    if (!response.ok) {
      if (response.status === 404) {
        return {
          success: false,
          message: `Invoice with bill number ${billNo} not found`,
        };
      }
      throw new Error(`Failed to fetch invoice: ${response.statusText}`);
    }

    return response.json();
  },

  // Get all invoices (for filtering/searching)
  async getAllInvoices(params: {
    suppliers?: string[];
    statuses?: string[];
    cost_centers?: string[];
    date_from?: string;
    date_to?: string;
    search?: string;
  } = {}): Promise<Invoice[]> {
    const searchParams = new URLSearchParams();
    
    // Convert arrays to dot-separated strings for API compatibility
    if (params.suppliers?.length) {
      searchParams.append('suppliers', params.suppliers.join('.'));
    }
    if (params.statuses?.length) {
      searchParams.append('statuses', params.statuses.join('.'));
    }
    if (params.cost_centers?.length) {
      searchParams.append('cost_centers', params.cost_centers.join('.'));
    }
    if (params.date_from) {
      searchParams.append('date_from', params.date_from);
    }
    if (params.date_to) {
      searchParams.append('date_to', params.date_to);
    }
    if (params.search) {
      searchParams.append('search', params.search);
    }

    // Get all results by setting a high limit
    searchParams.append('limit', '1000');

    const response = await fetch(`/api/invoices?${searchParams.toString()}`);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch invoices: ${response.statusText}`);
    }

    const data: InvoiceResponse = await response.json();
    return data.invoices;
  },
};
