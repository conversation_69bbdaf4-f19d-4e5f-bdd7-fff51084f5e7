"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, XAxis, YAxis } from "recharts"; // Add YAxis import

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";

export const description = "An interactive bar chart";

const chartData = [
  { date: "2025-06-01", monthong: 178, chanee: 200 },
  { date: "2025-06-02", monthong: 470, chanee: 410 },
  { date: "2025-06-03", monthong: 103, chanee: 160 },
  { date: "2025-06-04", monthong: 439, chanee: 380 },
  { date: "2025-06-05", monthong: 88, chanee: 140 },
  { date: "2025-06-06", monthong: 294, chanee: 250 },
  { date: "2025-06-07", monthong: 323, chanee: 370 },
  { date: "2025-06-08", monthong: 385, chanee: 320 },
  { date: "2025-06-09", monthong: 438, chanee: 480 },
  { date: "2025-06-10", monthong: 155, chanee: 200 },
  { date: "2025-06-11", monthong: 92, chanee: 150 },
  { date: "2025-06-12", monthong: 492, chanee: 420 },
  { date: "2025-06-13", monthong: 81, chanee: 130 },
  { date: "2025-06-14", monthong: 426, chanee: 380 },
  { date: "2025-06-15", monthong: 307, chanee: 350 },
  { date: "2025-06-16", monthong: 371, chanee: 310 },
  { date: "2025-06-17", monthong: 475, chanee: 520 },
  { date: "2025-06-18", monthong: 107, chanee: 170 },
  { date: "2025-06-19", monthong: 341, chanee: 290 },
  { date: "2025-06-20", monthong: 408, chanee: 450 },
  { date: "2025-06-21", monthong: 169, chanee: 210 },
  { date: "2025-06-22", monthong: 317, chanee: 270 },
  { date: "2025-06-23", monthong: 480, chanee: 530 },
  { date: "2025-06-24", monthong: 132, chanee: 180 },
  { date: "2025-06-25", monthong: 141, chanee: 190 },
  { date: "2025-06-26", monthong: 434, chanee: 380 },
  { date: "2025-06-27", monthong: 448, chanee: 490 },
  { date: "2025-06-28", monthong: 149, chanee: 200 },
  { date: "2025-06-29", monthong: 103, chanee: 160 },
  { date: "2025-06-30", monthong: 446, chanee: 400 },
];

const chartConfig = {
  views: {
    label: "Weights (kg)",
    color: "var(--primary)",
  },
  monthong: {
    label: "monthong",
    color: "var(--primary)",
  },
  chanee: {
    label: "chanee",
    color: "var(--primary)",
  },
  error: {
    label: "Error",
    color: "var(--primary)",
  },
} satisfies ChartConfig;

export function TopVolumeByDurianTypeChart() {
  const [activeChart, setActiveChart] =
    React.useState<keyof typeof chartConfig>("monthong");

  const total = React.useMemo(
    () => ({
      monthong: chartData.reduce((acc, curr) => acc + curr.monthong, 0),
      chanee: chartData.reduce((acc, curr) => acc + curr.chanee, 0),
    }),
    []
  );

  const [isClient, setIsClient] = React.useState(false);

  React.useEffect(() => {
    setIsClient(true);
  }, []);

  React.useEffect(() => {
    if (activeChart === "error") {
      throw new Error("Mocking Error");
    }
  }, [activeChart]);

  if (!isClient) {
    return null;
  }

  return (
    <Card className="@container/card pt-0">
      <CardHeader className="flex flex-col items-stretch space-y-0 border-b !p-0 sm:flex-row">
        <div className="flex flex-1 flex-col justify-center gap-1 px-6 !py-0">
          <CardTitle>Top Volume by Durian Type</CardTitle>
          <CardDescription>
            <span className="hidden @[540px]/card:block">
              Total for the last 30 days
            </span>
            <span className="@[540px]/card:hidden">Last 3 months</span>
          </CardDescription>
        </div>
        <div className="flex">
          {["monthong", "chanee"].map((key) => {
            const chart = key as keyof typeof chartConfig;
            if (!chart || total[key as keyof typeof total] === 0) return null;
            return (
              <button
                key={chart}
                data-active={activeChart === chart}
                className="data-[active=true]:bg-primary/5 hover:bg-primary/5 relative flex flex-1 flex-col justify-center gap-1 border-t px-6 py-4 text-left transition-colors duration-200 even:border-l sm:border-t-0 sm:border-l sm:px-8 sm:py-6"
                onClick={() => setActiveChart(chart)}
              >
                <span className="text-muted-foreground text-xs">
                  {chartConfig[chart].label.toLocaleUpperCase()}
                </span>
                <span className="text-lg leading-none font-bold sm:text-xl">
                  {total[key as keyof typeof total].toLocaleString()}
                  <span className="text-xs">Kg</span>
                </span>
              </button>
            );
          })}
        </div>
      </CardHeader>
      <CardContent className="px-2 pt-4 sm:px-6 sm:pt-6">
        <ChartContainer
          config={chartConfig}
          className="aspect-auto h-[280px] w-full"
        >
          <BarChart
            data={chartData}
            margin={{
              left: 12,
              right: 12,
            }}
          >
            <defs>
              <linearGradient id="fillBar" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="0%"
                  stopColor="var(--primary)"
                  stopOpacity={0.8}
                />
                <stop
                  offset="100%"
                  stopColor="var(--primary)"
                  stopOpacity={0.2}
                />
              </linearGradient>
            </defs>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              minTickGap={32}
              tickFormatter={(value) => {
                const date = new Date(value);
                return date.toLocaleDateString("en-US", {
                  month: "short",
                  day: "numeric",
                });
              }}
            />
            <YAxis
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              width={60}
              label={{
                value: "Weight (kg)",
                angle: -90,
                position: "insideLeft",
                style: { textAnchor: "middle" },
              }}
            />
            <ChartTooltip
              cursor={{ fill: "var(--primary)", opacity: 0.1 }}
              content={
                <ChartTooltipContent
                  className="w-[150px]"
                  nameKey="views"
                  labelFormatter={(value) => {
                    return new Date(value).toLocaleDateString("en-US", {
                      month: "short",
                      day: "numeric",
                      year: "numeric",
                    });
                  }}
                />
              }
            />
            <Bar
              dataKey={activeChart}
              fill="url(#fillBar)"
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
